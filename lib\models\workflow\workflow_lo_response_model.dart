// To parse this JSON data, do
//
//     final workFlowLoResponseModel = workFlowLoResponseModelFromJson(jsonString);

import 'dart:convert';

WorkFlowLoResponseModel workFlowLoResponseModelFromJson(String str) => WorkFlowLoResponseModel.fromJson(json.decode(str));

String workFlowLoResponseModelToJson(WorkFlowLoResponseModel data) => json.encode(data.toJson());

class WorkFlowLoResponseModel {
    bool? success;
    List<String>? messages;
    ParsedData? parsedData;
    List<ValidationError>? validationErrors;
    ParsedGos? parsedGos;
    ParsedLos? parsedLos;

    WorkFlowLoResponseModel({
        this.success,
        this.messages,
        this.parsedData,
        this.validationErrors,
        this.parsedGos,
        this.parsedLos,
    });

    WorkFlowLoResponseModel copyWith({
        bool? success,
        List<String>? messages,
        ParsedData? parsedData,
        List<ValidationError>? validationErrors,
        ParsedGos? parsedGos,
        ParsedLos? parsedLos,
    }) => 
        WorkFlowLoResponseModel(
            success: success ?? this.success,
            messages: messages ?? this.messages,
            parsedData: parsedData ?? this.parsedData,
            validationErrors: validationErrors ?? this.validationErrors,
            parsedGos: parsedGos ?? this.parsedGos,
            parsedLos: parsedLos ?? this.parsedLos,
        );

    factory WorkFlowLoResponseModel.fromJson(Map<String, dynamic> json) => WorkFlowLoResponseModel(
        success: json["success"],
        messages: json["messages"] == null ? [] : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null ? null : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"] == null ? [] : List<ValidationError>.from(json["validation_errors"]!.map((x) => ValidationError.fromJson(x))),
        parsedGos: json["parsed_gos"] == null ? null : ParsedGos.fromJson(json["parsed_gos"]),
        parsedLos: json["parsed_los"] == null ? null : ParsedLos.fromJson(json["parsed_los"]),
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "messages": messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors == null ? [] : List<dynamic>.from(validationErrors!.map((x) => x.toJson())),
        "parsed_gos": parsedGos?.toJson(),
        "parsed_los": parsedLos?.toJson(),
    };
}

class ParsedData {
    ParsedLos? localObjectives;
    LoPutStack? loInputStack;
    List<LoInputItem>? loInputItems;
    LoPutStack? loOutputStack;
    List<LoOutputItem>? loOutputItems;
    List<LoEntityValidation>? loEntityValidations;
    LoUiStack? loUiStack;
    List<LoUiEntityAttributeStack>? loUiEntityAttributeStack;
    LoDataMappingStack? loDataMappingStack;
    List<LoDataMapping>? loDataMappings;
    List<LoNestedFunction>? loNestedFunctions;
    List<ExecutionPathway>? executionPathways;
    List<Map<String, String?>>? executionPathwayConditions;
    List<LoNestedFunctionPutStack>? loNestedFunctionInputStacks;
    List<LoNestedFunctionOutputItem>? loNestedFunctionOutputItems;
    List<LoNestedFunctionPutStack>? loNestedFunctionOutputStacks;

    ParsedData({
        this.localObjectives,
        this.loInputStack,
        this.loInputItems,
        this.loOutputStack,
        this.loOutputItems,
        this.loEntityValidations,
        this.loUiStack,
        this.loUiEntityAttributeStack,
        this.loDataMappingStack,
        this.loDataMappings,
        this.loNestedFunctions,
        this.executionPathways,
        this.executionPathwayConditions,
        this.loNestedFunctionInputStacks,
        this.loNestedFunctionOutputItems,
        this.loNestedFunctionOutputStacks,
    });

    ParsedData copyWith({
        ParsedLos? localObjectives,
        LoPutStack? loInputStack,
        List<LoInputItem>? loInputItems,
        LoPutStack? loOutputStack,
        List<LoOutputItem>? loOutputItems,
        List<LoEntityValidation>? loEntityValidations,
        LoUiStack? loUiStack,
        List<LoUiEntityAttributeStack>? loUiEntityAttributeStack,
        LoDataMappingStack? loDataMappingStack,
        List<LoDataMapping>? loDataMappings,
        List<LoNestedFunction>? loNestedFunctions,
        List<ExecutionPathway>? executionPathways,
        List<Map<String, String?>>? executionPathwayConditions,
        List<LoNestedFunctionPutStack>? loNestedFunctionInputStacks,
        List<LoNestedFunctionOutputItem>? loNestedFunctionOutputItems,
        List<LoNestedFunctionPutStack>? loNestedFunctionOutputStacks,
    }) => 
        ParsedData(
            localObjectives: localObjectives ?? this.localObjectives,
            loInputStack: loInputStack ?? this.loInputStack,
            loInputItems: loInputItems ?? this.loInputItems,
            loOutputStack: loOutputStack ?? this.loOutputStack,
            loOutputItems: loOutputItems ?? this.loOutputItems,
            loEntityValidations: loEntityValidations ?? this.loEntityValidations,
            loUiStack: loUiStack ?? this.loUiStack,
            loUiEntityAttributeStack: loUiEntityAttributeStack ?? this.loUiEntityAttributeStack,
            loDataMappingStack: loDataMappingStack ?? this.loDataMappingStack,
            loDataMappings: loDataMappings ?? this.loDataMappings,
            loNestedFunctions: loNestedFunctions ?? this.loNestedFunctions,
            executionPathways: executionPathways ?? this.executionPathways,
            executionPathwayConditions: executionPathwayConditions ?? this.executionPathwayConditions,
            loNestedFunctionInputStacks: loNestedFunctionInputStacks ?? this.loNestedFunctionInputStacks,
            loNestedFunctionOutputItems: loNestedFunctionOutputItems ?? this.loNestedFunctionOutputItems,
            loNestedFunctionOutputStacks: loNestedFunctionOutputStacks ?? this.loNestedFunctionOutputStacks,
        );

    factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        localObjectives: json["local_objectives"] == null ? null : ParsedLos.fromJson(json["local_objectives"]),
        loInputStack: json["lo_input_stack"] == null ? null : LoPutStack.fromJson(json["lo_input_stack"]),
        loInputItems: json["lo_input_items"] == null ? [] : List<LoInputItem>.from(json["lo_input_items"]!.map((x) => LoInputItem.fromJson(x))),
        loOutputStack: json["lo_output_stack"] == null ? null : LoPutStack.fromJson(json["lo_output_stack"]),
        loOutputItems: json["lo_output_items"] == null ? [] : List<LoOutputItem>.from(json["lo_output_items"]!.map((x) => LoOutputItem.fromJson(x))),
        loEntityValidations: json["lo_entity_validations"] == null ? [] : List<LoEntityValidation>.from(json["lo_entity_validations"]!.map((x) => LoEntityValidation.fromJson(x))),
        loUiStack: json["lo_ui_stack"] == null ? null : LoUiStack.fromJson(json["lo_ui_stack"]),
        loUiEntityAttributeStack: json["lo_ui_entity_attribute_stack"] == null ? [] : List<LoUiEntityAttributeStack>.from(json["lo_ui_entity_attribute_stack"]!.map((x) => LoUiEntityAttributeStack.fromJson(x))),
        loDataMappingStack: json["lo_data_mapping_stack"] == null ? null : LoDataMappingStack.fromJson(json["lo_data_mapping_stack"]),
        loDataMappings: json["lo_data_mappings"] == null ? [] : List<LoDataMapping>.from(json["lo_data_mappings"]!.map((x) => LoDataMapping.fromJson(x))),
        loNestedFunctions: json["lo_nested_functions"] == null ? [] : List<LoNestedFunction>.from(json["lo_nested_functions"]!.map((x) => LoNestedFunction.fromJson(x))),
        executionPathways: json["execution_pathways"] == null ? [] : List<ExecutionPathway>.from(json["execution_pathways"]!.map((x) => ExecutionPathway.fromJson(x))),
        executionPathwayConditions: json["execution_pathway_conditions"] == null ? [] : List<Map<String, String?>>.from(json["execution_pathway_conditions"]!.map((x) => Map.from(x).map((k, v) => MapEntry<String, String?>(k, v)))),
        loNestedFunctionInputStacks: json["lo_nested_function_input_stacks"] == null ? [] : List<LoNestedFunctionPutStack>.from(json["lo_nested_function_input_stacks"]!.map((x) => LoNestedFunctionPutStack.fromJson(x))),
        loNestedFunctionOutputItems: json["lo_nested_function_output_items"] == null ? [] : List<LoNestedFunctionOutputItem>.from(json["lo_nested_function_output_items"]!.map((x) => LoNestedFunctionOutputItem.fromJson(x))),
        loNestedFunctionOutputStacks: json["lo_nested_function_output_stacks"] == null ? [] : List<LoNestedFunctionPutStack>.from(json["lo_nested_function_output_stacks"]!.map((x) => LoNestedFunctionPutStack.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "local_objectives": localObjectives?.toJson(),
        "lo_input_stack": loInputStack?.toJson(),
        "lo_input_items": loInputItems == null ? [] : List<dynamic>.from(loInputItems!.map((x) => x.toJson())),
        "lo_output_stack": loOutputStack?.toJson(),
        "lo_output_items": loOutputItems == null ? [] : List<dynamic>.from(loOutputItems!.map((x) => x.toJson())),
        "lo_entity_validations": loEntityValidations == null ? [] : List<dynamic>.from(loEntityValidations!.map((x) => x.toJson())),
        "lo_ui_stack": loUiStack?.toJson(),
        "lo_ui_entity_attribute_stack": loUiEntityAttributeStack == null ? [] : List<dynamic>.from(loUiEntityAttributeStack!.map((x) => x.toJson())),
        "lo_data_mapping_stack": loDataMappingStack?.toJson(),
        "lo_data_mappings": loDataMappings == null ? [] : List<dynamic>.from(loDataMappings!.map((x) => x.toJson())),
        "lo_nested_functions": loNestedFunctions == null ? [] : List<dynamic>.from(loNestedFunctions!.map((x) => x.toJson())),
        "execution_pathways": executionPathways == null ? [] : List<dynamic>.from(executionPathways!.map((x) => x.toJson())),
        "execution_pathway_conditions": executionPathwayConditions == null ? [] : List<dynamic>.from(executionPathwayConditions!.map((x) => Map.from(x).map((k, v) => MapEntry<String, dynamic>(k, v)))),
        "lo_nested_function_input_stacks": loNestedFunctionInputStacks == null ? [] : List<dynamic>.from(loNestedFunctionInputStacks!.map((x) => x.toJson())),
        "lo_nested_function_output_items": loNestedFunctionOutputItems == null ? [] : List<dynamic>.from(loNestedFunctionOutputItems!.map((x) => x.toJson())),
        "lo_nested_function_output_stacks": loNestedFunctionOutputStacks == null ? [] : List<dynamic>.from(loNestedFunctionOutputStacks!.map((x) => x.toJson())),
    };
}

class ExecutionPathway {
    String? id;
    String? executionPathwayId;
    LoId? loId;
    String? pathwayType;
    String? nextLo;
    dynamic createdAt;
    dynamic createdBy;
    dynamic updatedAt;
    dynamic updatedBy;

    ExecutionPathway({
        this.id,
        this.executionPathwayId,
        this.loId,
        this.pathwayType,
        this.nextLo,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
    });

    ExecutionPathway copyWith({
        String? id,
        String? executionPathwayId,
        LoId? loId,
        String? pathwayType,
        String? nextLo,
        dynamic createdAt,
        dynamic createdBy,
        dynamic updatedAt,
        dynamic updatedBy,
    }) => 
        ExecutionPathway(
            id: id ?? this.id,
            executionPathwayId: executionPathwayId ?? this.executionPathwayId,
            loId: loId ?? this.loId,
            pathwayType: pathwayType ?? this.pathwayType,
            nextLo: nextLo ?? this.nextLo,
            createdAt: createdAt ?? this.createdAt,
            createdBy: createdBy ?? this.createdBy,
            updatedAt: updatedAt ?? this.updatedAt,
            updatedBy: updatedBy ?? this.updatedBy,
        );

    factory ExecutionPathway.fromJson(Map<String, dynamic> json) => ExecutionPathway(
        id: json["id"],
        executionPathwayId: json["execution_pathway_id"],
        loId: loIdValues.map[json["lo_id"]]!,
        pathwayType: json["pathway_type"],
        nextLo: json["next_lo"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "execution_pathway_id": executionPathwayId,
        "lo_id": loIdValues.reverse[loId],
        "pathway_type": pathwayType,
        "next_lo": nextLo,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
    };
}

enum LoId {
    GO1_LO1
}

final loIdValues = EnumValues({
    "GO1.LO1": LoId.GO1_LO1
});

class LoDataMappingStack {
    String? id;
    String? loDataMappingStackId;
    LoId? loId;
    String? description;
    dynamic createdAt;
    dynamic createdBy;
    dynamic updatedBy;
    dynamic updatedAt;

    LoDataMappingStack({
        this.id,
        this.loDataMappingStackId,
        this.loId,
        this.description,
        this.createdAt,
        this.createdBy,
        this.updatedBy,
        this.updatedAt,
    });

    LoDataMappingStack copyWith({
        String? id,
        String? loDataMappingStackId,
        LoId? loId,
        String? description,
        dynamic createdAt,
        dynamic createdBy,
        dynamic updatedBy,
        dynamic updatedAt,
    }) => 
        LoDataMappingStack(
            id: id ?? this.id,
            loDataMappingStackId: loDataMappingStackId ?? this.loDataMappingStackId,
            loId: loId ?? this.loId,
            description: description ?? this.description,
            createdAt: createdAt ?? this.createdAt,
            createdBy: createdBy ?? this.createdBy,
            updatedBy: updatedBy ?? this.updatedBy,
            updatedAt: updatedAt ?? this.updatedAt,
        );

    factory LoDataMappingStack.fromJson(Map<String, dynamic> json) => LoDataMappingStack(
        id: json["id"],
        loDataMappingStackId: json["lo_data_mapping_stack_id"],
        loId: loIdValues.map[json["lo_id"]]!,
        description: json["description"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        updatedAt: json["updated_at"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_data_mapping_stack_id": loDataMappingStackId,
        "lo_id": loIdValues.reverse[loId],
        "description": description,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "updated_at": updatedAt,
    };
}

class LoDataMapping {
    String? id;
    String? loDataMappingItemId;
    String? mappingStack;
    String? sourceOutputStackItemId;
    SourceEntity? sourceEntity;
    String? sourceAttribute;
    String? targetInputStackItemId;
    String? targetEntity;
    String? targetAttribute;
    String? mappingType;

    LoDataMapping({
        this.id,
        this.loDataMappingItemId,
        this.mappingStack,
        this.sourceOutputStackItemId,
        this.sourceEntity,
        this.sourceAttribute,
        this.targetInputStackItemId,
        this.targetEntity,
        this.targetAttribute,
        this.mappingType,
    });

    LoDataMapping copyWith({
        String? id,
        String? loDataMappingItemId,
        String? mappingStack,
        String? sourceOutputStackItemId,
        SourceEntity? sourceEntity,
        String? sourceAttribute,
        String? targetInputStackItemId,
        String? targetEntity,
        String? targetAttribute,
        String? mappingType,
    }) => 
        LoDataMapping(
            id: id ?? this.id,
            loDataMappingItemId: loDataMappingItemId ?? this.loDataMappingItemId,
            mappingStack: mappingStack ?? this.mappingStack,
            sourceOutputStackItemId: sourceOutputStackItemId ?? this.sourceOutputStackItemId,
            sourceEntity: sourceEntity ?? this.sourceEntity,
            sourceAttribute: sourceAttribute ?? this.sourceAttribute,
            targetInputStackItemId: targetInputStackItemId ?? this.targetInputStackItemId,
            targetEntity: targetEntity ?? this.targetEntity,
            targetAttribute: targetAttribute ?? this.targetAttribute,
            mappingType: mappingType ?? this.mappingType,
        );

    factory LoDataMapping.fromJson(Map<String, dynamic> json) => LoDataMapping(
        id: json["id"],
        loDataMappingItemId: json["lo_data_mapping_item_id"],
        mappingStack: json["mapping_stack"],
        sourceOutputStackItemId: json["source_output_stack_item_id"],
        sourceEntity: sourceEntityValues.map[json["source_entity"]]!,
        sourceAttribute: json["source_attribute"],
        targetInputStackItemId: json["target_input_stack_item_id"],
        targetEntity: json["target_entity"],
        targetAttribute: json["target_attribute"],
        mappingType: json["mapping_type"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_data_mapping_item_id": loDataMappingItemId,
        "mapping_stack": mappingStack,
        "source_output_stack_item_id": sourceOutputStackItemId,
        "source_entity": sourceEntityValues.reverse[sourceEntity],
        "source_attribute": sourceAttribute,
        "target_input_stack_item_id": targetInputStackItemId,
        "target_entity": targetEntity,
        "target_attribute": targetAttribute,
        "mapping_type": mappingType,
    };
}

enum SourceEntity {
    LEAVE_APPLICATION
}

final sourceEntityValues = EnumValues({
    "LeaveApplication": SourceEntity.LEAVE_APPLICATION
});

class LoEntityValidation {
    String? loEntityValidationId;
    LoId? loId;
    String? loInputItemsId;
    SourceEntity? entityId;
    String? attributeId;
    String? validationCondition;
    String? errorMessage;

    LoEntityValidation({
        this.loEntityValidationId,
        this.loId,
        this.loInputItemsId,
        this.entityId,
        this.attributeId,
        this.validationCondition,
        this.errorMessage,
    });

    LoEntityValidation copyWith({
        String? loEntityValidationId,
        LoId? loId,
        String? loInputItemsId,
        SourceEntity? entityId,
        String? attributeId,
        String? validationCondition,
        String? errorMessage,
    }) => 
        LoEntityValidation(
            loEntityValidationId: loEntityValidationId ?? this.loEntityValidationId,
            loId: loId ?? this.loId,
            loInputItemsId: loInputItemsId ?? this.loInputItemsId,
            entityId: entityId ?? this.entityId,
            attributeId: attributeId ?? this.attributeId,
            validationCondition: validationCondition ?? this.validationCondition,
            errorMessage: errorMessage ?? this.errorMessage,
        );

    factory LoEntityValidation.fromJson(Map<String, dynamic> json) => LoEntityValidation(
        loEntityValidationId: json["lo_entity_validation_id"],
        loId: loIdValues.map[json["lo_id"]]!,
        loInputItemsId: json["lo_input_items_id"],
        entityId: sourceEntityValues.map[json["entity_id"]]!,
        attributeId: json["attribute_id"],
        validationCondition: json["validation_condition"],
        errorMessage: json["error_message"],
    );

    Map<String, dynamic> toJson() => {
        "lo_entity_validation_id": loEntityValidationId,
        "lo_id": loIdValues.reverse[loId],
        "lo_input_items_id": loInputItemsId,
        "entity_id": sourceEntityValues.reverse[entityId],
        "attribute_id": attributeId,
        "validation_condition": validationCondition,
        "error_message": errorMessage,
    };
}

class LoInputItem {
    String? id;
    InputStackId? inputStackId;
    String? slotId;
    bool? required;
    DataTypeEnum? dataType;
    bool? dependentAttribute;
    String? dependentAttributeValue;
    dynamic enumValues;
    bool? readOnly;
    AgentType? agentType;
    SourceType? sourceType;
    String? sourceDescription;
    String? nestedFunctionId;

    LoInputItem({
        this.id,
        this.inputStackId,
        this.slotId,
        this.required,
        this.dataType,
        this.dependentAttribute,
        this.dependentAttributeValue,
        this.enumValues,
        this.readOnly,
        this.agentType,
        this.sourceType,
        this.sourceDescription,
        this.nestedFunctionId,
    });

    LoInputItem copyWith({
        String? id,
        InputStackId? inputStackId,
        String? slotId,
        bool? required,
        DataTypeEnum? dataType,
        bool? dependentAttribute,
        String? dependentAttributeValue,
        dynamic enumValues,
        bool? readOnly,
        AgentType? agentType,
        SourceType? sourceType,
        String? sourceDescription,
        String? nestedFunctionId,
    }) => 
        LoInputItem(
            id: id ?? this.id,
            inputStackId: inputStackId ?? this.inputStackId,
            slotId: slotId ?? this.slotId,
            required: required ?? this.required,
            dataType: dataType ?? this.dataType,
            dependentAttribute: dependentAttribute ?? this.dependentAttribute,
            dependentAttributeValue: dependentAttributeValue ?? this.dependentAttributeValue,
            enumValues: enumValues ?? this.enumValues,
            readOnly: readOnly ?? this.readOnly,
            agentType: agentType ?? this.agentType,
            sourceType: sourceType ?? this.sourceType,
            sourceDescription: sourceDescription ?? this.sourceDescription,
            nestedFunctionId: nestedFunctionId ?? this.nestedFunctionId,
        );

    factory LoInputItem.fromJson(Map<String, dynamic> json) => LoInputItem(
        id: json["id"],
        inputStackId: inputStackIdValues.map[json["input_stack_id"]]!,
        slotId: json["slot_id"],
        required: json["required"],
        dataType: dataTypeEnumValues.map[json["data_type"]]!,
        dependentAttribute: json["dependent_attribute"],
        dependentAttributeValue: json["dependent_attribute_value"],
        enumValues: json["enum_values"],
        readOnly: json["read_only"],
        agentType: agentTypeValues.map[json["agent_type"]]!,
        sourceType: sourceTypeValues.map[json["source_type"]]!,
        sourceDescription: json["source_description"],
        nestedFunctionId: json["nested_function_id"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "input_stack_id": inputStackIdValues.reverse[inputStackId],
        "slot_id": slotId,
        "required": required,
        "data_type": dataTypeEnumValues.reverse[dataType],
        "dependent_attribute": dependentAttribute,
        "dependent_attribute_value": dependentAttributeValue,
        "enum_values": enumValues,
        "read_only": readOnly,
        "agent_type": agentTypeValues.reverse[agentType],
        "source_type": sourceTypeValues.reverse[sourceType],
        "source_description": sourceDescription,
        "nested_function_id": nestedFunctionId,
    };
}

enum AgentType {
    DIGITAL,
    HUMAN
}

final agentTypeValues = EnumValues({
    "DIGITAL": AgentType.DIGITAL,
    "HUMAN": AgentType.HUMAN
});

enum DataTypeEnum {
    BOOLEAN,
    DATE,
    DATETIME,
    NUMBER,
    STRING
}

final dataTypeEnumValues = EnumValues({
    "boolean": DataTypeEnum.BOOLEAN,
    "date": DataTypeEnum.DATE,
    "datetime": DataTypeEnum.DATETIME,
    "number": DataTypeEnum.NUMBER,
    "string": DataTypeEnum.STRING
});

enum InputStackId {
    GO1_LO1_IP1
}

final inputStackIdValues = EnumValues({
    "GO1.LO1.IP1": InputStackId.GO1_LO1_IP1
});

enum SourceType {
    SYSTEM,
    USER
}

final sourceTypeValues = EnumValues({
    "system": SourceType.SYSTEM,
    "user": SourceType.USER
});

class LoPutStack {
    String? id;
    LoId? loId;
    String? description;

    LoPutStack({
        this.id,
        this.loId,
        this.description,
    });

    LoPutStack copyWith({
        String? id,
        LoId? loId,
        String? description,
    }) => 
        LoPutStack(
            id: id ?? this.id,
            loId: loId ?? this.loId,
            description: description ?? this.description,
        );

    factory LoPutStack.fromJson(Map<String, dynamic> json) => LoPutStack(
        id: json["id"],
        loId: loIdValues.map[json["lo_id"]]!,
        description: json["description"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loIdValues.reverse[loId],
        "description": description,
    };
}

class LoNestedFunctionPutStack {
    String? id;
    LoId? loId;
    String? functionName;
    String? stackName;
    String? stackDescription;
    StackType? stackType;
    bool? isActive;
    dynamic createdAt;
    dynamic createdBy;
    dynamic updatedAt;
    dynamic updatedBy;

    LoNestedFunctionPutStack({
        this.id,
        this.loId,
        this.functionName,
        this.stackName,
        this.stackDescription,
        this.stackType,
        this.isActive,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
    });

    LoNestedFunctionPutStack copyWith({
        String? id,
        LoId? loId,
        String? functionName,
        String? stackName,
        String? stackDescription,
        StackType? stackType,
        bool? isActive,
        dynamic createdAt,
        dynamic createdBy,
        dynamic updatedAt,
        dynamic updatedBy,
    }) => 
        LoNestedFunctionPutStack(
            id: id ?? this.id,
            loId: loId ?? this.loId,
            functionName: functionName ?? this.functionName,
            stackName: stackName ?? this.stackName,
            stackDescription: stackDescription ?? this.stackDescription,
            stackType: stackType ?? this.stackType,
            isActive: isActive ?? this.isActive,
            createdAt: createdAt ?? this.createdAt,
            createdBy: createdBy ?? this.createdBy,
            updatedAt: updatedAt ?? this.updatedAt,
            updatedBy: updatedBy ?? this.updatedBy,
        );

    factory LoNestedFunctionPutStack.fromJson(Map<String, dynamic> json) => LoNestedFunctionPutStack(
        id: json["id"],
        loId: loIdValues.map[json["lo_id"]]!,
        functionName: json["function_name"],
        stackName: json["stack_name"],
        stackDescription: json["stack_description"],
        stackType: stackTypeValues.map[json["stack_type"]]!,
        isActive: json["is_active"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loIdValues.reverse[loId],
        "function_name": functionName,
        "stack_name": stackName,
        "stack_description": stackDescription,
        "stack_type": stackTypeValues.reverse[stackType],
        "is_active": isActive,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
    };
}

enum StackType {
    INPUT,
    OUTPUT
}

final stackTypeValues = EnumValues({
    "input": StackType.INPUT,
    "output": StackType.OUTPUT
});

class LoNestedFunctionOutputItem {
    String? id;
    LoId? loId;
    String? functionName;
    String? outputName;
    String? outputDescription;
    DataTypeEnum? dataType;
    bool? isRequired;
    dynamic format;
    dynamic createdAt;
    dynamic createdBy;
    dynamic updatedAt;
    dynamic updatedBy;

    LoNestedFunctionOutputItem({
        this.id,
        this.loId,
        this.functionName,
        this.outputName,
        this.outputDescription,
        this.dataType,
        this.isRequired,
        this.format,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
    });

    LoNestedFunctionOutputItem copyWith({
        String? id,
        LoId? loId,
        String? functionName,
        String? outputName,
        String? outputDescription,
        DataTypeEnum? dataType,
        bool? isRequired,
        dynamic format,
        dynamic createdAt,
        dynamic createdBy,
        dynamic updatedAt,
        dynamic updatedBy,
    }) => 
        LoNestedFunctionOutputItem(
            id: id ?? this.id,
            loId: loId ?? this.loId,
            functionName: functionName ?? this.functionName,
            outputName: outputName ?? this.outputName,
            outputDescription: outputDescription ?? this.outputDescription,
            dataType: dataType ?? this.dataType,
            isRequired: isRequired ?? this.isRequired,
            format: format ?? this.format,
            createdAt: createdAt ?? this.createdAt,
            createdBy: createdBy ?? this.createdBy,
            updatedAt: updatedAt ?? this.updatedAt,
            updatedBy: updatedBy ?? this.updatedBy,
        );

    factory LoNestedFunctionOutputItem.fromJson(Map<String, dynamic> json) => LoNestedFunctionOutputItem(
        id: json["id"],
        loId: loIdValues.map[json["lo_id"]]!,
        functionName: json["function_name"],
        outputName: json["output_name"],
        outputDescription: json["output_description"],
        dataType: dataTypeEnumValues.map[json["data_type"]]!,
        isRequired: json["is_required"],
        format: json["format"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loIdValues.reverse[loId],
        "function_name": functionName,
        "output_name": outputName,
        "output_description": outputDescription,
        "data_type": dataTypeEnumValues.reverse[dataType],
        "is_required": isRequired,
        "format": format,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
    };
}

class LoNestedFunction {
    String? id;
    LoId? loId;
    String? functionName;
    String? functionType;
    List<String>? functionParameters;
    String? description;
    String? returns;
    String? outputsTo;

    LoNestedFunction({
        this.id,
        this.loId,
        this.functionName,
        this.functionType,
        this.functionParameters,
        this.description,
        this.returns,
        this.outputsTo,
    });

    LoNestedFunction copyWith({
        String? id,
        LoId? loId,
        String? functionName,
        String? functionType,
        List<String>? functionParameters,
        String? description,
        String? returns,
        String? outputsTo,
    }) => 
        LoNestedFunction(
            id: id ?? this.id,
            loId: loId ?? this.loId,
            functionName: functionName ?? this.functionName,
            functionType: functionType ?? this.functionType,
            functionParameters: functionParameters ?? this.functionParameters,
            description: description ?? this.description,
            returns: returns ?? this.returns,
            outputsTo: outputsTo ?? this.outputsTo,
        );

    factory LoNestedFunction.fromJson(Map<String, dynamic> json) => LoNestedFunction(
        id: json["id"],
        loId: loIdValues.map[json["lo_id"]]!,
        functionName: json["function_name"],
        functionType: json["function_type"],
        functionParameters: json["function_parameters"] == null ? [] : List<String>.from(json["function_parameters"]!.map((x) => x)),
        description: json["description"],
        returns: json["returns"],
        outputsTo: json["outputs_to"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loIdValues.reverse[loId],
        "function_name": functionName,
        "function_type": functionType,
        "function_parameters": functionParameters == null ? [] : List<dynamic>.from(functionParameters!.map((x) => x)),
        "description": description,
        "returns": returns,
        "outputs_to": outputsTo,
    };
}

class LoOutputItem {
    String? id;
    OutputStackId? outputStackId;
    String? slotId;
    LoId? loId;
    DataTypeEnum? type;
    String? nestedFunctionId;

    LoOutputItem({
        this.id,
        this.outputStackId,
        this.slotId,
        this.loId,
        this.type,
        this.nestedFunctionId,
    });

    LoOutputItem copyWith({
        String? id,
        OutputStackId? outputStackId,
        String? slotId,
        LoId? loId,
        DataTypeEnum? type,
        String? nestedFunctionId,
    }) => 
        LoOutputItem(
            id: id ?? this.id,
            outputStackId: outputStackId ?? this.outputStackId,
            slotId: slotId ?? this.slotId,
            loId: loId ?? this.loId,
            type: type ?? this.type,
            nestedFunctionId: nestedFunctionId ?? this.nestedFunctionId,
        );

    factory LoOutputItem.fromJson(Map<String, dynamic> json) => LoOutputItem(
        id: json["id"],
        outputStackId: outputStackIdValues.map[json["output_stack_id"]]!,
        slotId: json["slot_id"],
        loId: loIdValues.map[json["lo_id"]]!,
        type: dataTypeEnumValues.map[json["type"]]!,
        nestedFunctionId: json["nested_function_id"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "output_stack_id": outputStackIdValues.reverse[outputStackId],
        "slot_id": slotId,
        "lo_id": loIdValues.reverse[loId],
        "type": dataTypeEnumValues.reverse[type],
        "nested_function_id": nestedFunctionId,
    };
}

enum OutputStackId {
    GO1_LO1_OP
}

final outputStackIdValues = EnumValues({
    "GO1.LO1.OP": OutputStackId.GO1_LO1_OP
});

class LoUiEntityAttributeStack {
    String? id;
    String? loUiEntityAttributeStackId;
    SourceEntity? entityId;
    String? attributeId;
    String? uiForm;
    StyleParameters? styleParameters;
    String? helperTip;
    bool? readOnly;

    LoUiEntityAttributeStack({
        this.id,
        this.loUiEntityAttributeStackId,
        this.entityId,
        this.attributeId,
        this.uiForm,
        this.styleParameters,
        this.helperTip,
        this.readOnly,
    });

    LoUiEntityAttributeStack copyWith({
        String? id,
        String? loUiEntityAttributeStackId,
        SourceEntity? entityId,
        String? attributeId,
        String? uiForm,
        StyleParameters? styleParameters,
        String? helperTip,
        bool? readOnly,
    }) => 
        LoUiEntityAttributeStack(
            id: id ?? this.id,
            loUiEntityAttributeStackId: loUiEntityAttributeStackId ?? this.loUiEntityAttributeStackId,
            entityId: entityId ?? this.entityId,
            attributeId: attributeId ?? this.attributeId,
            uiForm: uiForm ?? this.uiForm,
            styleParameters: styleParameters ?? this.styleParameters,
            helperTip: helperTip ?? this.helperTip,
            readOnly: readOnly ?? this.readOnly,
        );

    factory LoUiEntityAttributeStack.fromJson(Map<String, dynamic> json) => LoUiEntityAttributeStack(
        id: json["id"],
        loUiEntityAttributeStackId: json["lo_ui_entity_attribute_stack_id"],
        entityId: sourceEntityValues.map[json["entity_id"]]!,
        attributeId: json["attribute_id"],
        uiForm: json["ui_form"],
        styleParameters: json["style_parameters"] == null ? null : StyleParameters.fromJson(json["style_parameters"]),
        helperTip: json["helper_tip"],
        readOnly: json["read_only"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_ui_entity_attribute_stack_id": loUiEntityAttributeStackId,
        "entity_id": sourceEntityValues.reverse[entityId],
        "attribute_id": attributeId,
        "ui_form": uiForm,
        "style_parameters": styleParameters?.toJson(),
        "helper_tip": helperTip,
        "read_only": readOnly,
    };
}

class StyleParameters {
    String? minValue;
    int? rows;
    int? maxlength;
    String? source;
    String? formattingClass;

    StyleParameters({
        this.minValue,
        this.rows,
        this.maxlength,
        this.source,
        this.formattingClass,
    });

    StyleParameters copyWith({
        String? minValue,
        int? rows,
        int? maxlength,
        String? source,
        String? formattingClass,
    }) => 
        StyleParameters(
            minValue: minValue ?? this.minValue,
            rows: rows ?? this.rows,
            maxlength: maxlength ?? this.maxlength,
            source: source ?? this.source,
            formattingClass: formattingClass ?? this.formattingClass,
        );

    factory StyleParameters.fromJson(Map<String, dynamic> json) => StyleParameters(
        minValue: json["min_value"],
        rows: json["rows"],
        maxlength: json["maxlength"],
        source: json["source"],
        formattingClass: json["formatting_class"],
    );

    Map<String, dynamic> toJson() => {
        "min_value": minValue,
        "rows": rows,
        "maxlength": maxlength,
        "source": source,
        "formatting_class": formattingClass,
    };
}

class LoUiStack {
    String? id;
    String? loUiStackId;
    String? loUiForm;
    ParsedGos? loUiStyleParameters;

    LoUiStack({
        this.id,
        this.loUiStackId,
        this.loUiForm,
        this.loUiStyleParameters,
    });

    LoUiStack copyWith({
        String? id,
        String? loUiStackId,
        String? loUiForm,
        ParsedGos? loUiStyleParameters,
    }) => 
        LoUiStack(
            id: id ?? this.id,
            loUiStackId: loUiStackId ?? this.loUiStackId,
            loUiForm: loUiForm ?? this.loUiForm,
            loUiStyleParameters: loUiStyleParameters ?? this.loUiStyleParameters,
        );

    factory LoUiStack.fromJson(Map<String, dynamic> json) => LoUiStack(
        id: json["id"],
        loUiStackId: json["lo_ui_stack_id"],
        loUiForm: json["lo_ui_form"],
        loUiStyleParameters: json["lo_ui_style_parameters"] == null ? null : ParsedGos.fromJson(json["lo_ui_style_parameters"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "lo_ui_stack_id": loUiStackId,
        "lo_ui_form": loUiForm,
        "lo_ui_style_parameters": loUiStyleParameters?.toJson(),
    };
}

class ParsedGos {
    ParsedGos();

    ParsedGos copyWith() => 
        ParsedGos(
        );

    factory ParsedGos.fromJson(Map<String, dynamic> json) => ParsedGos(
    );

    Map<String, dynamic> toJson() => {
    };
}

class ParsedLos {
    String? name;
    String? version;
    String? status;
    String? workflowSource;
    String? functionType;
    AgentType? agentType;
    String? executionRights;
    String? goId;
    LoId? loId;

    ParsedLos({
        this.name,
        this.version,
        this.status,
        this.workflowSource,
        this.functionType,
        this.agentType,
        this.executionRights,
        this.goId,
        this.loId,
    });

    ParsedLos copyWith({
        String? name,
        String? version,
        String? status,
        String? workflowSource,
        String? functionType,
        AgentType? agentType,
        String? executionRights,
        String? goId,
        LoId? loId,
    }) => 
        ParsedLos(
            name: name ?? this.name,
            version: version ?? this.version,
            status: status ?? this.status,
            workflowSource: workflowSource ?? this.workflowSource,
            functionType: functionType ?? this.functionType,
            agentType: agentType ?? this.agentType,
            executionRights: executionRights ?? this.executionRights,
            goId: goId ?? this.goId,
            loId: loId ?? this.loId,
        );

    factory ParsedLos.fromJson(Map<String, dynamic> json) => ParsedLos(
        name: json["name"],
        version: json["version"],
        status: json["status"],
        workflowSource: json["workflow_source"],
        functionType: json["function_type"],
        agentType: agentTypeValues.map[json["agent_type"]]!,
        executionRights: json["execution_rights"],
        goId: json["go_id"],
        loId: loIdValues.map[json["lo_id"]]!,
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "version": version,
        "status": status,
        "workflow_source": workflowSource,
        "function_type": functionType,
        "agent_type": agentTypeValues.reverse[agentType],
        "execution_rights": executionRights,
        "go_id": goId,
        "lo_id": loIdValues.reverse[loId],
    };
}

class ValidationError {
    ValidationErrorType? type;
    String? message;
    Location? location;

    ValidationError({
        this.type,
        this.message,
        this.location,
    });

    ValidationError copyWith({
        ValidationErrorType? type,
        String? message,
        Location? location,
    }) => 
        ValidationError(
            type: type ?? this.type,
            message: message ?? this.message,
            location: location ?? this.location,
        );

    factory ValidationError.fromJson(Map<String, dynamic> json) => ValidationError(
        type: validationErrorTypeValues.map[json["type"]]!,
        message: json["message"],
        location: locationValues.map[json["location"]]!,
    );

    Map<String, dynamic> toJson() => {
        "type": validationErrorTypeValues.reverse[type],
        "message": message,
        "location": locationValues.reverse[location],
    };
}

enum Location {
    LO
}

final locationValues = EnumValues({
    "lo": Location.LO
});

enum ValidationErrorType {
    VALIDATION
}

final validationErrorTypeValues = EnumValues({
    "validation": ValidationErrorType.VALIDATION
});

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
            reverseMap = map.map((k, v) => MapEntry(v, k));
            return reverseMap;
    }
}
