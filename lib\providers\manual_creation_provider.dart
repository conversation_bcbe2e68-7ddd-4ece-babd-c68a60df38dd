import 'package:flutter/material.dart';
import 'package:nsl/models/role_info.dart';
import '../models/agent_manual_response_model.dart';
import '../models/agent_data.dart';
import '../models/workflow/entity_manual_response_model.dart';
import '../models/workflow/workflow_manual_response_model.dart';
import '../models/workflow/workflow_lo_response_model.dart';
import '../models/entities_data.dart' as entities_model;
import '../services/text_validation_service.dart';
import '../services/entity_validation_service.dart';
import '../services/workflow_validation_service.dart';
import '../services/workflow_lo_validation_service.dart';
import '../utils/logger.dart';

enum WorkflowStep { agentCreation, entityCreation, workflowCreation, workflowLoCreation }

class ManualCreationProvider extends ChangeNotifier {
  late TextEditingController _textController;
  String? _hoveredIcon;

  // Workflow state
  WorkflowStep _currentStep = WorkflowStep.agentCreation;

  // Side panel state
  bool _showSidePanel = false;
  RoleInfo? _selectedRole;
  double _sidePanelWidth = 480.0;
  final double _minSidePanelWidth = 250.0;
  final double _maxSidePanelWidth = 600.0;

  // Agent validation state
  bool _isValidating = false;
  AgentModelResponseModel? _validationResult;
  String? _validationError;
  bool _showAgentTable = false;
  AgentData? _extractedAgentData;

  // Entity validation state
  bool _isValidatingEntity = false;
  EntityModelResponseModel? _entityValidationResult;
  String? _entityValidationError;
  bool _showEntityTable = false;
  entities_model.EntitiesData? _extractedEntityData;

  // Workflow validation state
  bool _isValidatingWorkflow = false;
  WorkFlowModelResponseModel? _workflowValidationResult;
  String? _workflowValidationError;
  bool _showWorkflowTable = false;
  List<Map<String, dynamic>>? _extractedWorkflowData;

  // WorkflowLO validation state
  bool _isValidatingWorkflowLo = false;
  WorkFlowLoResponseModel? _workflowLoValidationResult;
  String? _workflowLoValidationError;
  bool _showWorkflowLoTable = false;
  List<Map<String, dynamic>>? _extractedWorkflowLoData;

  // Service instances
  final TextValidationService _validationService = TextValidationService();
  final EntityValidationService _entityValidationService =
      EntityValidationService();
  final WorkflowValidationService _workflowValidationService =
      WorkflowValidationService();
  final WorkflowLoValidationService _workflowLoValidationService =
      WorkflowLoValidationService();

  ManualCreationProvider() {
    _textController = TextEditingController();
  }

  // Getters
  TextEditingController get textController => _textController;
  String? get hoveredIcon => _hoveredIcon;

  // Workflow getters
  WorkflowStep get currentStep => _currentStep;

  // Agent validation getters
  bool get isValidating => _isValidating;
  AgentModelResponseModel? get validationResult => _validationResult;
  String? get validationError => _validationError;
  bool get showAgentTable => _showAgentTable;
  AgentData? get extractedAgentData => _extractedAgentData;

  // Entity validation getters
  bool get isValidatingEntity => _isValidatingEntity;
  EntityModelResponseModel? get entityValidationResult =>
      _entityValidationResult;
  String? get entityValidationError => _entityValidationError;
  bool get showEntityTable => _showEntityTable;
  entities_model.EntitiesData? get extractedEntityData => _extractedEntityData;

  // Workflow validation getters
  bool get isValidatingWorkflow => _isValidatingWorkflow;
  WorkFlowModelResponseModel? get workflowValidationResult =>
      _workflowValidationResult;
  String? get workflowValidationError => _workflowValidationError;
  bool get showWorkflowTable => _showWorkflowTable;
  List<Map<String, dynamic>>? get extractedWorkflowData =>
      _extractedWorkflowData;

  // WorkflowLO validation getters
  bool get isValidatingWorkflowLo => _isValidatingWorkflowLo;
  WorkFlowLoResponseModel? get workflowLoValidationResult =>
      _workflowLoValidationResult;
  String? get workflowLoValidationError => _workflowLoValidationError;
  bool get showWorkflowLoTable => _showWorkflowLoTable;
  List<Map<String, dynamic>>? get extractedWorkflowLoData =>
      _extractedWorkflowLoData;

// Side panel getters
  bool get showSidePanel => _showSidePanel;
  RoleInfo? get selectedRole => _selectedRole;
  double get sidePanelWidth => _sidePanelWidth;
  double get minSidePanelWidth => _minSidePanelWidth;
  double get maxSidePanelWidth => _maxSidePanelWidth;

  // User data getter
  List<User>? get extractedUsers {
    if (_validationResult?.parsedUsers != null) {
      return _validationResult!.parsedUsers!.values.toList();
    }
    return null;
  }

  // Get linear workflow data for linear tree visualization
  Map<String, dynamic>? getLinearWorkflowData() {
    print('🌳 Getting linear workflow data...');
    Logger.info('🌳 Getting linear workflow data...');

    if (_extractedWorkflowData == null || _extractedWorkflowData!.isEmpty) {
      print('🌳 No extracted workflow data available for linear display');
      Logger.info('🌳 No extracted workflow data available for linear display');
      return null;
    }

    try {
      // Use the first workflow data
      final workflowData = _extractedWorkflowData!.first;
      final workFlowDetails = workflowData['workFlowDetails'];

      print('🌳 Processing workflow data for linear display...');
      Logger.info('🌳 Processing workflow data for linear display...');

      // Extract basic workflow information from API response
      String title = _extractWorkflowTitle(workFlowDetails);
      String subtitle = _extractWorkflowSubtitle(workFlowDetails);

      // Extract sequential steps from process flow
      List<String> sequentialSteps = _extractSequentialSteps(workFlowDetails);

      // Extract hierarchical breakdown
      List<Map<String, dynamic>> hierarchicalSteps =
          _extractHierarchicalSteps(workFlowDetails);

      Map<String, dynamic> linearData = {
        'title': title,
        'subtitle': subtitle,
        'sequentialSteps': sequentialSteps,
        'hierarchicalSteps': hierarchicalSteps,
      };

      print('🌳 Generated linear workflow data: $linearData');
      Logger.info(
          '🌳 Generated linear workflow data with ${sequentialSteps.length} sequential steps and ${hierarchicalSteps.length} hierarchical steps');

      // Debug log the hierarchical structure
      _logHierarchicalStructure(hierarchicalSteps);

      return linearData;
    } catch (e) {
      print('🌳 Error creating linear workflow data: $e');
      Logger.error('🌳 Error creating linear workflow data: $e');
      return null;
    }
  }

  // Helper method to extract sequential steps for the one-line display
  List<String> _extractSequentialSteps(dynamic workFlowDetails) {
    List<String> steps = [];

    try {
      // First try to extract from process flow (most detailed)
      // if (workFlowDetails?.processFlow != null && workFlowDetails.processFlow.isNotEmpty) {
      //   for (var flow in workFlowDetails.processFlow) {
      //     if (flow.loName != null && flow.loName.isNotEmpty) {
      //       steps.add(flow.loName);
      //     }
      //   }
      //   Logger.info('🌳 Extracted ${steps.length} steps from processFlow');
      // }

      // // If no process flow, try to extract from local objectives
      // if (steps.isEmpty && workFlowDetails?.localObjectivesList != null && workFlowDetails.localObjectivesList.isNotEmpty) {
      //   for (var lo in workFlowDetails.localObjectivesList) {
      //     if (lo.loName != null && lo.loName.isNotEmpty) {
      //       steps.add(lo.loName);
      //     }
      //   }
      //   Logger.info('🌳 Extracted ${steps.length} steps from localObjectivesList');
      // }

      // If still no steps, try pathway definitions
      if (steps.isEmpty &&
          workFlowDetails?.pathwayDefinitions != null &&
          workFlowDetails.pathwayDefinitions.isNotEmpty) {
        // Extract step names from pathway definitions
        for (var pathway in workFlowDetails.pathwayDefinitions) {
          if (pathway.pathwayName != null && pathway.pathwayName.isNotEmpty) {
            steps.add(pathway.pathwayName);
          }
        }
        Logger.info(
            '🌳 Extracted ${steps.length} steps from pathwayDefinitions');
      }

      // Return empty list if no data found - no fallback
      if (steps.isEmpty) {
        Logger.warning('🌳 No sequential steps found in API response');
      }
    } catch (e) {
      Logger.error('Error extracting sequential steps: $e');
      // Return empty list on error - no fallback
    }

    return steps;
  }

  // Helper method to extract hierarchical steps for the tree breakdown
  List<Map<String, dynamic>> _extractHierarchicalSteps(
      dynamic workFlowDetails) {
    List<Map<String, dynamic>> hierarchicalSteps = [];

    try {
      // Build complete hierarchical structure: GO → Pathways → LOs
      hierarchicalSteps = _buildCompleteWorkflowHierarchy(workFlowDetails);
      Logger.info(
          '🌳 Built complete workflow hierarchy with ${hierarchicalSteps.length} root steps');

      // Return empty list if no data found - no fallback
      if (hierarchicalSteps.isEmpty) {
        Logger.warning('🌳 No hierarchical steps found in API response');
      }
    } catch (e) {
      Logger.error('Error extracting hierarchical steps: $e');
      // Return empty list on error - no fallback
    }

    return hierarchicalSteps;
  }

  // Build complete workflow hierarchy: GO → Pathways → LOs with series/parallel flows
  List<Map<String, dynamic>> _buildCompleteWorkflowHierarchy(
      dynamic workFlowDetails) {
    List<Map<String, dynamic>> hierarchy = [];

    try {
      // Create root node from Global Objectives
      String goTitle =
          workFlowDetails?.globalObjectives?.name ?? 'Global Objective';

      Map<String, dynamic> goNode = {
        'level': 0,
        'text': goTitle,
        'flowType': 'root',
        'children': <Map<String, dynamic>>[],
      };

      // Add pathway definitions as children of GO
      if (workFlowDetails?.pathwayDefinitions != null &&
          workFlowDetails.pathwayDefinitions.isNotEmpty) {
        List<Map<String, dynamic>> pathwayChildren = [];

        for (var pathway in workFlowDetails.pathwayDefinitions) {
          Map<String, dynamic> pathwayNode = {
            'level': 1,
            'text': pathway.pathwayName ?? 'Unknown Pathway',
            'flowType': 'pathway',
            'children': <Map<String, dynamic>>[],
          };

          // Parse pathway steps with series/parallel flow logic
          if (pathway.steps != null && pathway.steps.isNotEmpty) {
            List<Map<String, dynamic>> stepChildren =
                _parsePathwaySteps(pathway.steps);
            pathwayNode['children'] = stepChildren;
          }

          pathwayChildren.add(pathwayNode);
        }

        goNode['children'] = pathwayChildren;
      }

      hierarchy.add(goNode);
    } catch (e) {
      Logger.error('Error building complete workflow hierarchy: $e');
    }

    return hierarchy;
  }

  // Parse pathway steps to identify series and parallel flows
  List<Map<String, dynamic>> _parsePathwaySteps(List<String> steps) {
    List<Map<String, dynamic>> parsedSteps = [];

    try {
      for (String step in steps) {
        step = step.trim();

        // Check if this is a parallel flow (contains parentheses or brackets)
        if (step.contains('(') || step.contains('[')) {
          // Parse parallel flow - returns list of parallel LOs
          List<Map<String, dynamic>> parallelLOs = _parseParallelFlow(step);
          parsedSteps.addAll(parallelLOs); // Add all parallel LOs at same level
        } else {
          // Parse as series flow (single LO)
          Map<String, dynamic> seriesStep = _parseSeriesStep(step);
          parsedSteps.add(seriesStep);
        }
      }
    } catch (e) {
      Logger.error('Error parsing pathway steps: $e');
    }

    return parsedSteps;
  }

  // Parse parallel flow steps (e.g., "(LO-7, LO-9)" or "[LO-7, LO-9]")
  // Returns list of individual parallel LOs instead of wrapper node
  List<Map<String, dynamic>> _parseParallelFlow(String step) {
    List<Map<String, dynamic>> parallelLOs = [];

    try {
      // Remove parentheses or brackets and split by comma
      String cleanStep = step.replaceAll(RegExp(r'[()[\]]'), '').trim();
      List<String> parallelLOsRefs =
          cleanStep.split(',').map((s) => s.trim()).toList();

      // Create individual nodes for each parallel LO
      for (String loRef in parallelLOsRefs) {
        if (loRef.isNotEmpty) {
          String loName = _mapLORefToName(loRef);
          parallelLOs.add({
            'level': 1,
            'text': loName,
            'flowType': 'parallel_item', // Mark as parallel item
            'loRef': loRef,
            'children': <Map<String, dynamic>>[],
          });
        }
      }

      Logger.info(
          '🔄 Parsed parallel flow: $step → ${parallelLOs.length} parallel LOs');
    } catch (e) {
      Logger.error('Error parsing parallel flow: $e');
      // Return error node if parsing fails
      parallelLOs.add({
        'level': 1,
        'text': 'Error parsing: $step',
        'flowType': 'error',
        'children': <Map<String, dynamic>>[],
      });
    }

    return parallelLOs;
  }

  // Parse series flow step (e.g., "LO-1")
  Map<String, dynamic> _parseSeriesStep(String step) {
    try {
      String loName = _mapLORefToName(step);
      return {
        'level': 1,
        'text': loName,
        'flowType': 'series',
        'loRef': step,
        'children': <Map<String, dynamic>>[],
      };
    } catch (e) {
      Logger.error('Error parsing series step: $e');
      return {
        'level': 1,
        'text': 'Error parsing: $step',
        'flowType': 'error',
        'children': <Map<String, dynamic>>[],
      };
    }
  }

  // Map LO reference (e.g., "LO-1") to actual LO name from localObjectivesList
  String _mapLORefToName(String loRef) {
    try {
      // Extract LO number from reference (e.g., "LO-1" -> 1)
      String cleanRef = loRef.trim().toUpperCase();
      if (cleanRef.startsWith('LO-')) {
        String numberStr = cleanRef.substring(3);
        int? loNumber = int.tryParse(numberStr);

        if (loNumber != null &&
            _workflowValidationResult?.parsedData?.go?.localObjectivesList !=
                null) {
          // Find matching LO in localObjectivesList
          for (var lo in _workflowValidationResult!
              .parsedData!.go!.localObjectivesList!) {
            if (lo.loNumber == loNumber) {
              return lo.loName ?? 'LO-$loNumber';
            }
          }
        }
      }

      // Fallback: return the original reference if no mapping found
      return loRef;
    } catch (e) {
      Logger.error('Error mapping LO reference: $e');
      return loRef;
    }
  }

  // Debug method to log hierarchical structure
  void _logHierarchicalStructure(List<Map<String, dynamic>> hierarchicalSteps,
      [int depth = 0]) {
    for (var step in hierarchicalSteps) {
      String indent = '  ' * depth;
      String flowType = step['flowType'] ?? '';
      String text = step['text'] ?? '';
      Logger.info('🌳 $indent[$flowType] $text');

      List<Map<String, dynamic>> children = step['children'] ?? [];
      if (children.isNotEmpty) {
        _logHierarchicalStructure(children, depth + 1);
      }
    }
  }

  // Helper method to extract workflow title from API response
  String _extractWorkflowTitle(dynamic workFlowDetails) {
    try {
      // Try to get title from global objectives
      if (workFlowDetails?.globalObjectives?.name != null &&
          workFlowDetails.globalObjectives.name.isNotEmpty) {
        return workFlowDetails.globalObjectives.name;
      }

      // Return empty string if no title found - no fallback
      return '';
    } catch (e) {
      Logger.error('Error extracting workflow title: $e');
      return '';
    }
  }

  // Helper method to extract workflow subtitle from API response
  String _extractWorkflowSubtitle(dynamic workFlowDetails) {
    try {
      // Try to get description from global objectives
      if (workFlowDetails?.globalObjectives?.description != null &&
          workFlowDetails.globalObjectives.description.isNotEmpty) {
        return workFlowDetails.globalObjectives.description;
      }

      // Try to get primary entity as subtitle
      if (workFlowDetails?.globalObjectives?.primaryEntity != null &&
          workFlowDetails.globalObjectives.primaryEntity.isNotEmpty) {
        return 'Process for ${workFlowDetails.globalObjectives.primaryEntity}';
      }

      // Try to get trigger condition as subtitle
      if (workFlowDetails?.triggerDefinition?.triggerCondition != null &&
          workFlowDetails.triggerDefinition.triggerCondition.isNotEmpty) {
        return workFlowDetails.triggerDefinition.triggerCondition;
      }

      // Return empty string if no subtitle found - no fallback
      return '';
    } catch (e) {
      Logger.error('Error extracting workflow subtitle: $e');
      return '';
    }
  }

  // Methods
  void setHoveredIcon(String? iconName) {
    _hoveredIcon = iconName;
    notifyListeners();
  }

  void clearHoveredIcon() {
    _hoveredIcon = null;
    notifyListeners();
  }

  void clearText() {
    _textController.clear();
    _selectedRole = null;
    _showSidePanel = false;
    clearValidationResults();
    notifyListeners();
  }

  void clearValidationResults() {
    _validationResult = null;
    _validationError = null;
    _isValidating = false;
    _showAgentTable = false;
    _extractedAgentData = null;
  }

  void clearEntityValidationResults() {
    _entityValidationResult = null;
    _entityValidationError = null;
    _isValidatingEntity = false;
    _showEntityTable = false;
    _extractedEntityData = null;
  }

  void clearWorkflowValidationResults() {
    _workflowValidationResult = null;
    _workflowValidationError = null;
    _isValidatingWorkflow = false;
    _showWorkflowTable = false;
    _extractedWorkflowData = null;
  }

  void clearWorkflowLoValidationResults() {
    _workflowLoValidationResult = null;
    _workflowLoValidationError = null;
    _isValidatingWorkflowLo = false;
    _showWorkflowLoTable = false;
    _extractedWorkflowLoData = null;
  }

  // Workflow navigation methods
  void goToNextStep() {
    if (_currentStep == WorkflowStep.agentCreation) {
      _currentStep = WorkflowStep.entityCreation;
      // Clear text field for entity input
      _textController.clear();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.entityCreation) {
      _currentStep = WorkflowStep.workflowCreation;
      // Clear text field for workflow input
      _textController.clear();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.workflowCreation) {
      _currentStep = WorkflowStep.workflowLoCreation;
      // Clear text field for workflowLO input
      _textController.clear();
      notifyListeners();
    }
  }

  void goToPreviousStep() {
    if (_currentStep == WorkflowStep.entityCreation) {
      _currentStep = WorkflowStep.agentCreation;
      // Clear entity validation results
      clearEntityValidationResults();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.workflowCreation) {
      _currentStep = WorkflowStep.entityCreation;
      // Clear workflow validation results
      clearWorkflowValidationResults();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.workflowLoCreation) {
      _currentStep = WorkflowStep.workflowCreation;
      // Clear workflowLO validation results
      clearWorkflowLoValidationResults();
      notifyListeners();
    }
  }

  void resetWorkflow() {
    _currentStep = WorkflowStep.agentCreation;
    _textController.clear();
    clearValidationResults();
    clearEntityValidationResults();
    clearWorkflowValidationResults();
    clearWorkflowLoValidationResults();
    notifyListeners();
  }

  void handleAgentsTap() {
    // Handle agents tap logic
    _currentStep = WorkflowStep.agentCreation;
    notifyListeners();
  }

  void handleDataSetsTap() {
    // Handle datasets tap logic
    _currentStep = WorkflowStep.entityCreation;
    notifyListeners();
  }

  void handleWorkflowsTap() {
    // Handle workflows tap logic
    _currentStep = WorkflowStep.workflowCreation;
    notifyListeners();
  }

  // Side panel management methods
  void showRoleDetailsPanel(RoleInfo role) {
    _selectedRole = role;
    _showSidePanel = true;
    notifyListeners();
  }

  void hideSidePanel() {
    _showSidePanel = false;
    _selectedRole = null;
    notifyListeners();
  }

  void updateSidePanelWidth(double width) {
    if (width >= _minSidePanelWidth && width <= _maxSidePanelWidth) {
      _sidePanelWidth = width;
      notifyListeners();
    }
  }

  void handleValidate() async {
    // Handle validate action based on current step
    if (_currentStep == WorkflowStep.agentCreation) {
      await _handleAgentValidation();
    } else if (_currentStep == WorkflowStep.entityCreation) {
      await _handleEntityValidation();
    } else if (_currentStep == WorkflowStep.workflowCreation) {
      await _handleWorkflowValidation();
    } else if (_currentStep == WorkflowStep.workflowLoCreation) {
      await _handleWorkflowLoValidation();
    }
  }

  Future<void> _handleAgentValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _validationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _validationError = null;
    _validationResult = null;
    _isValidating = true;
    notifyListeners();

    try {
      Logger.info('Starting agent text validation...');
      final result = await _validationService.validateText(text);

      _validationResult = result;
      _isValidating = false;
      _showAgentTable = true;
      _extractedAgentData = _extractAgentDataFromResponse(result);
      // Check if validation was successful (no validation errors)
      // if (result.validationErrors == null || result.validationErrors!.isEmpty) {
      //   _showAgentTable = true;
      //   _extractedAgentData = _extractAgentDataFromResponse(result);
      //   Logger.info(
      //       'Agent text validation completed successfully - showing agent table');
      // }

      Logger.info('Agent text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _validationError = e.toString().replaceFirst('Exception: ', '');
      _isValidating = false;
      Logger.error('Agent text validation failed: $e');
      notifyListeners();
    }
  }

  Future<void> _handleEntityValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _entityValidationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _entityValidationError = null;
    _entityValidationResult = null;
    _isValidatingEntity = true;
    notifyListeners();

    try {
      Logger.info('Starting entity text validation...');
      final result = await _entityValidationService.validateText(text);

      _entityValidationResult = result;
      _isValidatingEntity = false;

      // Check if validation was successful
      if (result.success == true) {
        _showEntityTable = true;
        _extractedEntityData = _extractEntityDataFromResponse(result);
        Logger.info(
            'Entity text validation completed successfully - showing entity table');
      }

      Logger.info('Entity text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _entityValidationError = e.toString().replaceFirst('Exception: ', '');
      _isValidatingEntity = false;
      Logger.error('Entity text validation failed: $e');
      notifyListeners();
    }
  }

  Future<void> _handleWorkflowValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _workflowValidationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _workflowValidationError = null;
    _workflowValidationResult = null;
    _isValidatingWorkflow = true;
    notifyListeners();

    try {
      Logger.info('Starting workflow text validation...');
      final result = await _workflowValidationService.validateText(text);

      _workflowValidationResult = result;
      _isValidatingWorkflow = false;

      // Check if validation was successful
      if (result.success == true) {
        _showWorkflowTable = true;
        _extractedWorkflowData = _extractWorkflowDataFromResponse(result);
        Logger.info(
            'Workflow text validation completed successfully - showing workflow table');
        print('🌳 Workflow validation success: ${result.success}');
        print(
            '🌳 Extracted workflow data count: ${_extractedWorkflowData?.length ?? 0}');
      } else {
        print('🌳 Workflow validation failed - success: ${result.success}');
        // Let's still try to extract data and show the table for debugging
        _showWorkflowTable = true;
        _extractedWorkflowData = _extractWorkflowDataFromResponse(result);
        print(
            '🌳 Extracted workflow data count (despite failure): ${_extractedWorkflowData?.length ?? 0}');
      }

      Logger.info('Workflow text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _workflowValidationError = e.toString().replaceFirst('Exception: ', '');
      _isValidatingWorkflow = false;
      Logger.error('Workflow text validation failed: $e');
      notifyListeners();
    }
  }

  Future<void> _handleWorkflowLoValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _workflowLoValidationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _workflowLoValidationError = null;
    _workflowLoValidationResult = null;
    _isValidatingWorkflowLo = true;
    notifyListeners();

    try {
      Logger.info('Starting workflow LO text validation...');
      final result = await _workflowLoValidationService.validateText(text);

      _workflowLoValidationResult = result;
      _isValidatingWorkflowLo = false;
         _showWorkflowLoTable = true;
       _extractedWorkflowLoData = _extractWorkflowLoDataFromResponse(result);
     
      // Check if validation was successful
      // if (result.success == true) {
      //   _showWorkflowLoTable = true;
      //   _extractedWorkflowLoData = _extractWorkflowLoDataFromResponse(result);
      //   Logger.info(
      //       'Workflow LO text validation completed successfully - showing workflow LO table');
      // }

      Logger.info('Workflow LO text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _workflowLoValidationError = e.toString().replaceFirst('Exception: ', '');
      _isValidatingWorkflowLo = false;
      Logger.error('Workflow LO text validation failed: $e');
      notifyListeners();
    }
  }

  // Extract agent data from validation response
  AgentData _extractAgentDataFromResponse(AgentModelResponseModel response) {
    try {
      Logger.info('🔍 Extracting agent data from validation response');

      // Create a new AgentData object
      AgentData agentData = AgentData();
      List<AgentInfo> agentInfoList = [];

      // Check if we have parsed roles data
      if (response.parsedRoles != null) {
        Logger.info('🔍 Processing parsed roles data');

        final roles = response.parsedRoles!;

        // Process each role type
        roles.forEach((roleType, role) {
          _processRole(role, roleType, agentInfoList);
        });
      }

      // Create system info
      AgentSystemInfo systemInfo = AgentSystemInfo(
        agentCount: agentInfoList.length,
        bulletPoints: [
          "Agent data extracted from validation response",
          "Contains ${agentInfoList.length} roles",
          "Processed from manual text validation"
        ],
        headerText: "Roles and Use Cases",
        systemVersions: ['1.0'],
        selectedVersion: '1.0',
      );

      // Update the AgentData object
      agentData = AgentData(
        agents: agentInfoList,
        systemInfo: systemInfo,
      );

      Logger.info(
          '🔍 Successfully extracted agent data with ${agentInfoList.length} roles');
      return agentData;
    } catch (e) {
      Logger.error('Error extracting agent data from response: $e');
      return AgentData();
    }
  }

  // Helper method to process individual role
  void _processRole(
      Employee? role, String roleType, List<AgentInfo> agentInfoList) {
    if (role != null) {
      Logger.info('🔍 Processing $roleType role');

      // Create sections for the role
      List<AgentInfoSection> sections = [];

      // Core Responsibilities section
      if (role.coreResponsibilities != null &&
          role.coreResponsibilities!.isNotEmpty) {
        sections.add(AgentInfoSection(
          id: 'responsibilities_${roleType.toLowerCase().replaceAll(' ', '_')}',
          title: 'Core Responsibilities',
          abbreviation: 'CR',
          items: role.coreResponsibilities!,
        ));
      }

      // KPIs section
      if (role.kpis != null && role.kpis!.isNotEmpty) {
        sections.add(AgentInfoSection(
          id: 'kpis_${roleType.toLowerCase().replaceAll(' ', '_')}',
          title: 'Key Performance Indicators',
          abbreviation: 'KPI',
          items: role.kpis!,
        ));
      }

      // Decision Authority section
      if (role.decisionAuthority != null &&
          role.decisionAuthority!.isNotEmpty) {
        sections.add(AgentInfoSection(
          id: 'authority_${roleType.toLowerCase().replaceAll(' ', '_')}',
          title: 'Decision Authority',
          abbreviation: 'DA',
          items: role.decisionAuthority!,
        ));
      }

      String description = "is in";

      if (role.department != null) {
        description += " ${role.department} Department";
      }

      if (role.parentRole != null) {
        description += ", inherits  ${role.parentRole}";
      }

      if (role.reportsTo != null) {
        description += ", reports to  ${role.reportsTo}";
      }

      // Create AgentInfo for this role
      AgentInfo agentInfo = AgentInfo(
        id: 'role_${roleType.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}',
        title: role.name ?? roleType,
        description: description,
        // 'Department: ${role.department ?? 'N/A'}, Team: ${role.team ?? 'N/A'}',
        version: '1.0',
        createdBy: 'System',
        createdDate: DateTime.now(),
        modifiedBy: 'System',
        modifiedDate: DateTime.now(),
        sections: sections,
      );

      agentInfoList.add(agentInfo);
      Logger.info('🔍 Added $roleType role to agent list');
    }
  }

  // Extract entity data from validation response
  entities_model.EntitiesData _extractEntityDataFromResponse(
      EntityModelResponseModel response) {
    try {
      Logger.info('🔍 Extracting entity data from validation response');

      // Create entity groups list
      List<entities_model.EntityGroup> entityGroups = [];

      // Check if we have parsed entities data
      if (response.parsedEntities != null) {
        Logger.info('🔍 Processing parsed entities data');

        final entities = response.parsedEntities!;

        // Process each entity type
        _processEntityToGroup(entities.loan, 'Loan', entityGroups);
        _processEntityToGroup(entities.customer, 'Customer', entityGroups);
        _processEntityToGroup(entities.collateral, 'Collateral', entityGroups);
        _processEntityToGroup(entities.payment, 'Payment', entityGroups);
      }

      // Create system info
      entities_model.SystemInfo systemInfo = entities_model.SystemInfo(
        entityCount: entityGroups.length,
        bulletPoints: [
          "Entity data extracted from validation response",
          "Contains ${entityGroups.length} entity types",
          "Processed from manual text validation"
        ],
        headerText: "Entities and Relationships",
        enitiyVersions: ['1.0'],
      );

      // Create EntitiesData object
      entities_model.EntitiesData entityData = entities_model.EntitiesData(
        entityGroups: entityGroups,
        systemInfo: systemInfo,
      );

      Logger.info(
          '🔍 Successfully extracted entity data with ${entityGroups.length} entity groups');
      return entityData;
    } catch (e) {
      Logger.error('Error extracting entity data from response: $e');
      return entities_model.EntitiesData();
    }
  }

  // Helper method to process individual entity into EntityGroup
  void _processEntityToGroup(dynamic entity, String entityType,
      List<entities_model.EntityGroup> entityGroups) {
    if (entity != null) {
      Logger.info('🔍 Processing $entityType entity');

      // Create attributes list
      List<entities_model.Attribute> attributes = [];
      if (entity.attributes != null && entity.attributes!.isNotEmpty) {
        entity.attributes!.forEach((key, value) {
          attributes.add(entities_model.Attribute(
            name: key,
            type: value.dataType?.toString() ?? 'Unknown',
            required: value.primaryKey ?? false,
            isPk: value.primaryKey ?? false,
            description: value.name ?? key,
            isFk: value.foreignKey ?? false,
          ));
        });
      }

      // Create business rules list
      List<entities_model.BusinessRule> businessRules = [];
      if (entity.businessRules != null) {
        businessRules.add(entities_model.BusinessRule(
          name: 'Business Rules',
          description: 'Business rules defined for this entity',
        ));
      }

      // Create Entity object
      entities_model.Entity entityObj = entities_model.Entity(
        id: 'entity_${entityType.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}',
        title: entity.displayName ?? entityType,
        description: entity.description ?? 'Entity Type: $entityType',
        version: '1.0',
        expanded: false,
        checked: false,
        createdBy: 'System',
        createdDate: DateTime.now(),
        modifiedBy: 'System',
        modifiedDate: DateTime.now().toString(),
        attributes: attributes,
        businessRules: businessRules,
      );

      // Create EntityGroup
      entities_model.EntityGroup entityGroup = entities_model.EntityGroup(
        id: 'group_${entityType.toLowerCase()}',
        title: entityType,
        documentId: 'doc_${entityType.toLowerCase()}',
        coreCount: '1',
        enitiyVersions: ['1.0'],
        checked: false,
        entities: [entityObj],
      );

      entityGroups.add(entityGroup);
      Logger.info('🔍 Added $entityType entity group to entity list');
    }
  }

  // Extract workflow data from validation response
  List<Map<String, dynamic>> _extractWorkflowDataFromResponse(
      WorkFlowModelResponseModel response) {
    try {
      Logger.info(
          '🔍 Extracting enhanced workflow data from validation response');

      List<Map<String, dynamic>> extractedWorkflows = [];

      // Check if we have parsed data
      if (response.parsedData != null && response.parsedData!.go != null) {
        Logger.info('🔍 Processing parsed workflow data');

        final go = response.parsedData!.go!;

        // Extract comprehensive workflow data
        Map<String, dynamic> workflow = {
          'id': 'workflow_${DateTime.now().millisecondsSinceEpoch}',
          'title': go.globalObjectives?.name ?? 'Workflow from Validation',
          'description': go.globalObjectives?.description ??
              'Workflow created from manual validation',
          'version': go.globalObjectives?.version ?? '1.0',
          'status': go.globalObjectives?.status ?? 'Active',
          'createdBy': 'Manual Creation',
          'createdDate': DateTime.now().toString(),
          'modifiedBy': 'Manual Creation',
          'modifiedDate': DateTime.now().toString(),
          'mainTitle': go.globalObjectives?.name ?? 'Workflow from Validation',

          // Core Details
          'primaryEntity': go.globalObjectives?.primaryEntity ?? 'Unknown',
          'classification': go.globalObjectives?.classification ?? 'Process',
          'tenantName': go.globalObjectives?.tenantName ?? 'Unknown Tenant',
          'bookName': go.globalObjectives?.bookName ?? 'Unknown Book',
          'chapterName': go.globalObjectives?.chapterName ?? 'Unknown Chapter',

          // Process Ownership
          'processOwnership': _extractProcessOwnership(go),

          // Trigger Definition
          'triggerDefinition': _extractTriggerDefinition(go),

          // Local Objectives
          'localObjectives': _extractLocalObjectives(go),

          // Pathway Definitions
          'pathwayDefinitions': _extractPathwayDefinitions(go),

          // Business Rules
          'businessRules': _extractBusinessRules(go),

          // Performance Metadata
          'performanceMetadata': _extractPerformanceMetadata(go),

          // Process Flow
          'processFlow': _extractProcessFlow(go),

          // Tree structure for visualization
          'tree': _convertValidationDataToTree(go),

          // Raw workflow details
          'workFlowDetails': go,
        };

        extractedWorkflows.add(workflow);
        Logger.info(
            '🔍 Successfully created enhanced workflow from validation response');
      }

      Logger.info(
          '🔍 Successfully extracted ${extractedWorkflows.length} enhanced workflows from validation response');
      return extractedWorkflows;
    } catch (e) {
      Logger.error('Error extracting workflow data from response: $e');
      return [];
    }
  }

  // Helper method to extract process ownership
  Map<String, dynamic> _extractProcessOwnership(dynamic go) {
    try {
      if (go.processOwnership != null) {
        return {
          'originator': go.processOwnership.originator ?? 'Unknown',
          'processOwner': go.processOwnership.processOwner ?? 'Unknown',
          'businessSponsor': go.processOwnership.businessSponsor ?? 'Unknown',
        };
      }
    } catch (e) {
      Logger.error('Error extracting process ownership: $e');
    }
    return {
      'originator': 'Unknown',
      'processOwner': 'Unknown',
      'businessSponsor': 'Unknown',
    };
  }

  // Helper method to extract trigger definition
  Map<String, dynamic> _extractTriggerDefinition(dynamic go) {
    try {
      if (go.triggerDefinition != null) {
        return {
          'triggerType': go.triggerDefinition.triggerType ?? 'Unknown',
          'triggerCondition':
              go.triggerDefinition.triggerCondition ?? 'Unknown',
          'triggerSchedule': go.triggerDefinition.triggerSchedule ?? 'Unknown',
          'triggerAttributes': go.triggerDefinition.triggerAttributes ?? [],
        };
      }
    } catch (e) {
      Logger.error('Error extracting trigger definition: $e');
    }
    return {
      'triggerType': 'Unknown',
      'triggerCondition': 'Unknown',
      'triggerSchedule': 'Unknown',
      'triggerAttributes': [],
    };
  }

  // Helper method to extract local objectives
  List<Map<String, dynamic>> _extractLocalObjectives(dynamic go) {
    List<Map<String, dynamic>> localObjectives = [];
    try {
      if (go.localObjectivesList != null &&
          go.localObjectivesList!.isNotEmpty) {
        for (var lo in go.localObjectivesList!) {
          localObjectives.add({
            'loNumber': lo.loNumber ?? 0,
            'loName': lo.loName ?? 'Unknown',
            'actorType': lo.actorType ?? 'Unknown',
            'id': lo.id ?? 'Unknown',
            'workSource': lo.workSource ?? 'Unknown',
            'terminal': lo.terminal ?? false,
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting local objectives: $e');
    }
    return localObjectives;
  }

  // Helper method to extract pathway definitions
  List<Map<String, dynamic>> _extractPathwayDefinitions(dynamic go) {
    List<Map<String, dynamic>> pathways = [];
    try {
      if (go.pathwayDefinitions != null && go.pathwayDefinitions!.isNotEmpty) {
        for (var pathway in go.pathwayDefinitions!) {
          pathways.add({
            'id': pathway.id ?? 'Unknown',
            'pathwayNumber': pathway.pathwayNumber ?? 0,
            'pathwayName': pathway.pathwayName ?? 'Unknown',
            'steps': pathway.steps ?? [],
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting pathway definitions: $e');
    }
    return pathways;
  }

  // Helper method to extract business rules
  List<Map<String, dynamic>> _extractBusinessRules(dynamic go) {
    List<Map<String, dynamic>> businessRules = [];
    try {
      if (go.businessRules != null && go.businessRules!.isNotEmpty) {
        for (var rule in go.businessRules!) {
          businessRules.add({
            'id': rule.id ?? 'Unknown',
            'ruleName': rule.ruleName ?? 'Unknown',
            'ruleDescription': rule.ruleDescription ?? 'Unknown',
            'ruleInputs': rule.ruleInputs ?? [],
            'ruleOutputs': rule.ruleOutputs ?? [],
            'ruleErrorMessage': rule.ruleErrorMessage ?? 'Unknown',
            'ruleValidationType': rule.ruleValidationType ?? 'Unknown',
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting business rules: $e');
    }
    return businessRules;
  }

  // Helper method to extract performance metadata
  Map<String, dynamic> _extractPerformanceMetadata(dynamic go) {
    try {
      if (go.performanceMetadata != null &&
          go.performanceMetadata.metadataData != null) {
        final metadata = go.performanceMetadata.metadataData;
        return {
          'cycleTime': metadata.cycleTime ?? 'Unknown',
          'numberOfPathways': metadata.numberOfPathways ?? 0,
          'volumeMetrics': {
            'averageVolume': metadata.volumeMetrics?.averageVolume ?? 0,
            'peakVolume': metadata.volumeMetrics?.peakVolume ?? 0,
            'unit': metadata.volumeMetrics?.unit ?? 'Unknown',
          },
          'slaThresholds': {
            'managerReview': metadata.slaThresholds?.managerReview ?? 'Unknown',
            'notification': metadata.slaThresholds?.notification ?? 'Unknown',
            'systemProcessing':
                metadata.slaThresholds?.systemProcessing ?? 'Unknown',
          },
          'criticalLoPerformance': metadata.criticalLoPerformance ?? {},
        };
      }
    } catch (e) {
      Logger.error('Error extracting performance metadata: $e');
    }
    return {
      'cycleTime': 'Unknown',
      'numberOfPathways': 0,
      'volumeMetrics': {'averageVolume': 0, 'peakVolume': 0, 'unit': 'Unknown'},
      'slaThresholds': {
        'managerReview': 'Unknown',
        'notification': 'Unknown',
        'systemProcessing': 'Unknown'
      },
      'criticalLoPerformance': {},
    };
  }

  // Helper method to extract process flow
  List<Map<String, dynamic>> _extractProcessFlow(dynamic go) {
    List<Map<String, dynamic>> processFlow = [];
    try {
      if (go.processFlow != null && go.processFlow!.isNotEmpty) {
        for (var flow in go.processFlow!) {
          processFlow.add({
            'loName': flow.loName ?? 'Unknown',
            'actorType': flow.actorType ?? 'Unknown',
            'description': flow.description ?? 'Unknown',
            'routeType': flow.routeType ?? 'Unknown',
            'id': flow.id ?? 'Unknown',
            'conditions': flow.conditions ?? [],
            'routes': flow.routes ?? [],
            'parallelRoutes': flow.parallelRoutes ?? [],
            'joinAt': flow.joinAt ?? '',
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting process flow: $e');
    }
    return processFlow;
  }

  // Helper method to convert validation data to tree format
  List<Map<String, dynamic>> _convertValidationDataToTree(dynamic go) {
    List<Map<String, dynamic>> tree = [];

    try {
      // Create tree nodes from global objectives
      if (go.globalObjectives != null) {
        tree.add({
          'id': 'go_${DateTime.now().millisecondsSinceEpoch}',
          'text': go.globalObjectives.name ?? 'Global Objective',
          'level': 0,
          'sequence': 0,
          'altText': go.globalObjectives.description,
          'isRejection': false,
          'isApproval': false,
          'isParallel': false,
          'hasCheckmark': false,
          'hasX': false,
        });
      }

      // Add local objectives if available
      if (go.localObjectivesList != null &&
          go.localObjectivesList!.isNotEmpty) {
        for (int i = 0; i < go.localObjectivesList!.length; i++) {
          final lo = go.localObjectivesList![i];
          tree.add({
            'id': 'lo_${i}_${DateTime.now().millisecondsSinceEpoch}',
            'text': lo.loName ?? 'Local Objective ${i + 1}',
            'level': 1,
            'sequence': i,
            'altText': 'Actor: ${lo.actorType ?? 'Unknown'}',
            'isRejection': false,
            'isApproval': false,
            'isParallel': false,
            'hasCheckmark': false,
            'hasX': false,
          });
        }
      }

      // Add business rules if available
      if (go.businessRules != null && go.businessRules!.isNotEmpty) {
        for (int i = 0; i < go.businessRules!.length; i++) {
          final rule = go.businessRules![i];
          tree.add({
            'id': 'br_${i}_${DateTime.now().millisecondsSinceEpoch}',
            'text': rule.ruleName ?? 'Business Rule ${i + 1}',
            'level': 2,
            'sequence': i,
            'altText': rule.ruleDescription,
            'isRejection': false,
            'isApproval': false,
            'isParallel': false,
            'hasCheckmark': false,
            'hasX': false,
          });
        }
      }

      Logger.info(
          '🔍 Successfully converted validation data to tree format with ${tree.length} nodes');
    } catch (e) {
      Logger.error('Error converting validation data to tree: $e');
    }

    return tree;
  }

  // Extract workflow LO data from API response
  List<Map<String, dynamic>> _extractWorkflowLoDataFromResponse(
      WorkFlowLoResponseModel response) {
    List<Map<String, dynamic>> extractedData = [];

    try {
      Logger.info('🔍 Extracting workflow LO data from API response...');

      // Extract from parsedLos if available
      if (response.parsedLos != null) {
        final parsedLos = response.parsedLos!;

        Map<String, dynamic> loData = {
          'id': 'lo_${DateTime.now().millisecondsSinceEpoch}',
          'name': parsedLos.name ?? 'Workflow LO',
          'version': parsedLos.version ?? '1.0',
          'status': parsedLos.status ?? 'Active',
          'workflowSource': parsedLos.workflowSource ?? '',
          'functionType': parsedLos.functionType ?? '',
          'agentType': parsedLos.agentType?.toString() ?? 'DIGITAL',
          'executionRights': parsedLos.executionRights ?? '',
          'goId': parsedLos.goId ?? '',
          'loId': parsedLos.loId?.toString() ?? '',
        };

        extractedData.add(loData);
        Logger.info('🔍 Added workflow LO data: ${parsedLos.name}');
      }

      // Extract from parsedData.localObjectives if available
      if (response.parsedData?.localObjectives != null) {
        final localObjectives = response.parsedData!.localObjectives!;

        Map<String, dynamic> loData = {
          'id': 'lo_data_${DateTime.now().millisecondsSinceEpoch}',
          'name': localObjectives.name ?? 'Local Objectives',
          'version': localObjectives.version ?? '1.0',
          'status': localObjectives.status ?? 'Active',
          'workflowSource': localObjectives.workflowSource ?? '',
          'functionType': localObjectives.functionType ?? '',
          'agentType': localObjectives.agentType?.toString() ?? 'DIGITAL',
          'executionRights': localObjectives.executionRights ?? '',
          'goId': localObjectives.goId ?? '',
          'loId': localObjectives.loId?.toString() ?? '',
        };

        extractedData.add(loData);
        Logger.info('🔍 Added local objectives data: ${localObjectives.name}');
      }

      Logger.info(
          '🔍 Successfully extracted ${extractedData.length} workflow LO items from API response');
    } catch (e) {
      Logger.error('Error extracting workflow LO data from response: $e');
      // Return empty list on error
    }

    return extractedData;
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}
