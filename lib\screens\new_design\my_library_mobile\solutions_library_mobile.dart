import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';

import 'package:nsl/screens/new_design/my_library_mobile/books_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class SolutionMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final String versionNumber;

  SolutionMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 105.0,
    this.imageHeight = 140.0,
    this.versionNumber = "V00172",
  });

  factory SolutionMobile.fromJson(Map<String, dynamic> json) {
    return SolutionMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 105.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 140.0,
      versionNumber: json['versionNumber'] as String? ?? "V00172",
    );
  }
}

class SolutionsLibraryMobile extends StatefulWidget {
  const SolutionsLibraryMobile({super.key, this.showNavigationBar = true});

  final bool showNavigationBar;

  @override
  State<SolutionsLibraryMobile> createState() => _SolutionsLibraryMobileState();
}

class _SolutionsLibraryMobileState extends State<SolutionsLibraryMobile>
    with TickerProviderStateMixin {
  late List<SolutionMobile> solutions;
  bool isLoading = true;
  int selectedTabIndex = 1; // 0: Books, 1: Solutions, 2: Objects
  late List<AnimationController> _animationControllers;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  // JSON string containing solution data
  static const String solutionsJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Ecommerce Ecommerce",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Fashion & Apparel Fashion & Apparel",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Financial Advisory Financial Advisory",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Courier & Logistics Courier & Logistics",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fitness & Wellness Fitness & Wellness",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadSolutions();
  }

  void _loadSolutions() {
    try {
      // Parse the JSON string
      final data = json.decode(solutionsJsonString);

      // Convert to SolutionMobile objects
      final List<SolutionMobile> loadedSolutions = (data['books'] as List)
          .map((solutionJson) => SolutionMobile.fromJson(solutionJson))
          .toList();

      setState(() {
        solutions = loadedSolutions;
        isLoading = false;
      });

      // Initialize animations after loading solutions
      _initializeAnimations();
    } catch (e) {
      setState(() {
        solutions = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading solutions: $e');
    }
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      solutions.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<Offset>(
        begin: const Offset(-1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutCubic,
      ));
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ));
    }).toList();

    // Start animations with staggered delay
    _startStaggeredAnimations();
  }

  void _startStaggeredAnimations() {
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildSolutionsGrid(),
        ],
      ),
      floatingActionButton: widget.showNavigationBar
          ? SizedBox(
              width: 46,
              height: 46,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateBookMobile(),
                    ),
                  );
                },
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(Icons.add),
              ),
            )
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          // Hamburger menu icon
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          // Expanded widget to center the title
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('websolution.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Invisible spacer to balance the layout (same width as menu icon)
          const SizedBox(width: 56), // IconButton default width
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BooksLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              // Already on solutions screen, no navigation needed
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Search text field
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                // Search and filter icons
                _MobileSolutionSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSolutionSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Create solution button
          // SizedBox(
          //   width: double.infinity,
          //   height: 44,
          //   child: ElevatedButton(
          //     onPressed: () {},
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor: const Color(0xff0058FF),
          //       foregroundColor: Colors.white,
          //       elevation: 0,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(6),
          //       ),
          //     ),
          //     child: Text(
          //       AppLocalizations.of(context)
          //           .translate('websolution.createButtonText'),
          //       style: const TextStyle(
          //         fontSize: 16,
          //         fontWeight: FontWeight.w500,
          //         fontFamily: 'TiemposText',
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildSolutionsGrid() {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Solution grid
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : solutions.isEmpty
                      ? const Center(child: Text('No solutions found'))
                      : _buildMobileSolutionGrid(),
              const SizedBox(height: 20),
              // Pagination
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _MobileSolutionPaginationButton(
                    icon: const Icon(Icons.chevron_left, size: 20),
                    onPressed: () {
                      // Handle previous page
                    },
                  ),
                  const SizedBox(width: 8),
                  _MobileSolutionPaginationButton(
                    icon: const Icon(Icons.chevron_right, size: 20),
                    onPressed: () {
                      // Handle next page
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileSolutionGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine number of solutions per row based on screen width
        int solutionsPerRow = MediaQuery.of(context).size.width >= 600 ? 3 : 3;

        // Calculate rows needed
        int rowCount = (solutions.length / solutionsPerRow).ceil();

        // Calculate the width available for each solution
        double availableWidth = constraints.maxWidth;
        double spacing = 14.0;
        double solutionWidth =
            (availableWidth - (spacing * (solutionsPerRow - 1))) /
                solutionsPerRow;

        return Column(
          children: List.generate(rowCount, (rowIndex) {
            // Calculate start and end indices for this row
            int startIndex = rowIndex * solutionsPerRow;
            int endIndex = (startIndex + solutionsPerRow <= solutions.length)
                ? startIndex + solutionsPerRow
                : solutions.length;

            // Create a list of solutions for this row
            List<SolutionMobile> rowSolutions =
                solutions.sublist(startIndex, endIndex);

            return Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: rowSolutions.asMap().entries.map((entry) {
                  SolutionMobile solution = entry.value;
                  int globalIndex = solutions.indexOf(solution);

                  return SizedBox(
                    width: solutionWidth,
                    child: globalIndex < _animationControllers.length
                        ? SlideTransition(
                            position: _slideAnimations[globalIndex],
                            child: FadeTransition(
                              opacity: _fadeAnimations[globalIndex],
                              child: _MobileSolutionCard(
                                onTap: () {
                                  // Handle solution tap
                                },
                                child: _buildMobileSolutionCard(
                                  title: solution.title,
                                  subtitle: solution.subtitle,
                                  imageUrl: solution.imageUrl,
                                  isDraft: solution.isDraft,
                                  imageWidth: solutionWidth,
                                  imageHeight: (solutionWidth * 140) /
                                      105, // Maintain aspect ratio
                                  versionNumber: solution.versionNumber,
                                  index: globalIndex,
                                ),
                              ),
                            ),
                          )
                        : _MobileSolutionCard(
                            onTap: () {
                              // Handle solution tap
                            },
                            child: _buildMobileSolutionCard(
                              title: solution.title,
                              subtitle: solution.subtitle,
                              imageUrl: solution.imageUrl,
                              isDraft: solution.isDraft,
                              imageWidth: solutionWidth,
                              imageHeight: (solutionWidth * 140) /
                                  105, // Maintain aspect ratio
                              versionNumber: solution.versionNumber,
                              index: globalIndex,
                            ),
                          ),
                  );
                }).toList(),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildMobileSolutionCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 105.0,
    double imageHeight = 140.0,
    String versionNumber = "V00172",
    int index = 0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Solution cover
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.fill,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: imageHeight * 0.10,
                right: imageWidth * 0.14,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              height: 1.334,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: imageWidth,
          child: Text(
            'Version: 001',
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 11,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

// Mobile Solution SVG Button Widget
class _MobileSolutionSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSolutionSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSolutionSvgButton> createState() =>
      _MobileSolutionSvgButtonState();
}

class _MobileSolutionSvgButtonState extends State<_MobileSolutionSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Solution Pagination Button Widget
class _MobileSolutionPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _MobileSolutionPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_MobileSolutionPaginationButton> createState() =>
      _MobileSolutionPaginationButtonState();
}

class _MobileSolutionPaginationButtonState
    extends State<_MobileSolutionPaginationButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          borderRadius: isPressed ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: Center(
          child: Icon(
            widget.icon.icon,
            color: isPressed ? const Color(0xff0058FF) : Colors.black,
            size: widget.icon.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Solution Card Widget
class _MobileSolutionCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileSolutionCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileSolutionCard> createState() => _MobileSolutionCardState();
}

class _MobileSolutionCardState extends State<_MobileSolutionCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: widget.child,
    );
  }
}
