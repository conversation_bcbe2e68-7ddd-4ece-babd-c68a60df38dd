import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class Book {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  Book({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 136.0,
    this.imageHeight = 200.0,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 136.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 200.0,
    );
  }
}

// ignore: must_be_immutable
class WebSolutionsScreen extends StatefulWidget {
  WebSolutionsScreen({super.key, this.showNavigationBar = true});
  bool showNavigationBar = true;
  // const WebSolutionsScreen({super.key});

  @override
  State<WebSolutionsScreen> createState() => _WebSolutionsScreenState();
}

class _WebSolutionsScreenState extends State<WebSolutionsScreen>
    with TickerProviderStateMixin {
  late List<Book> books;
  bool isLoading = true;
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  // Pagination state
  int _currentPage = 1;
  int _itemsPerPage = 10; // Default for 1366px width
  int _totalPages = 1;

  // JSON string containing book data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Ecommerce Ecommerce",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Fashion & Apparel Fashion & Apparel",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder-01.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Financial Advisory Financial Advisory",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder-02.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
       "imageUrl": "assets/images/solution-placeholder-03.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder-04.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Courier & Logistics Courier & Logistics",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
       "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fitness & Wellness Fitness & Wellness",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
      {
      "title": "Real Estate Real Estate Real Estate",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      books.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: -50.0, end: 0.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // Start animations with staggered delays (left to right)
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadBooks() {
    try {
      // Parse the JSON string
      final data = json.decode(booksJsonString);

      // Convert to Book objects
      final List<Book> loadedBooks = (data['books'] as List)
          .map((bookJson) => Book.fromJson(bookJson))
          .toList();

      setState(() {
        books = loadedBooks;
        isLoading = false;
        _updatePagination();
      });

      // Initialize animations after books are loaded
      _initializeAnimations();
    } catch (e) {
      setState(() {
        books = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading books: $e');
    }
  }

  // Calculate items per page based on screen width and side panel state
  int _calculateItemsPerPage(double availableWidth) {
    const double cardWidth = 136.0;
    const double minSpacing = 80;

    // Calculate how many cards can fit in one row
    int booksPerRow =
        ((availableWidth + minSpacing) / (cardWidth + minSpacing)).floor();
    if (booksPerRow == 0) booksPerRow = 1;

    // For 1366px width, we want 2 rows (20 items total)
    // For smaller widths, adjust accordingly
    int rows = availableWidth >= 1366 ? 2 : (availableWidth >= 1000 ? 2 : 2);

    return booksPerRow * rows;
  }

  // Update pagination based on current state
  void _updatePagination() {
    if (books.isEmpty) {
      _totalPages = 1;
      return;
    }

    _totalPages = (books.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
    }
    if (_currentPage < 1) {
      _currentPage = 1;
    }
  }

  // Get books for current page
  List<Book> _getCurrentPageBooks() {
    if (books.isEmpty) return [];

    int startIndex = (_currentPage - 1) * _itemsPerPage;
    int endIndex = startIndex + _itemsPerPage;

    if (startIndex >= books.length) return [];
    if (endIndex > books.length) endIndex = books.length;

    return books.sublist(startIndex, endIndex);
  }

  // Navigate to previous page
  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  // Navigate to next page
  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation bar
          widget.showNavigationBar
              ? Padding(
                  padding: widget.showNavigationBar
                      ? const EdgeInsets.only(
                          left: 94, right: 94, bottom: 0.0, top: 0)
                      : EdgeInsets.zero,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: 316,
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      _HoverNavItem(
                                        iconPath:
                                            'assets/images/books-icon.svg',
                                        label: AppLocalizations.of(context)
                                            .translate('library.books'),
                                        isActive: Provider.of<WebHomeProvider>(
                                                    context)
                                                .currentScreenIndex ==
                                            ScreenConstants.webMyLibrary,
                                        onTap: () {
                                          Provider.of<WebHomeProvider>(context,
                                                      listen: false)
                                                  .currentScreenIndex =
                                              ScreenConstants.webMyLibrary;
                                        },
                                      ),
                                      _HoverNavItem(
                                        iconPath:
                                            'assets/images/square-box-uncheck.svg',
                                        label: AppLocalizations.of(context)
                                            .translate('library.solutions'),
                                        isActive: Provider.of<WebHomeProvider>(
                                                    context)
                                                .currentScreenIndex ==
                                            ScreenConstants.webMySolution,
                                        onTap: () {
                                          Provider.of<WebHomeProvider>(context,
                                                      listen: false)
                                                  .currentScreenIndex =
                                              ScreenConstants.webMySolution;
                                        },
                                      ),
                                      _HoverNavItem(
                                        iconPath: 'assets/images/cube-box.svg',
                                        label: AppLocalizations.of(context)
                                            .translate('library.objects'),
                                        isActive: Provider.of<WebHomeProvider>(
                                                    context)
                                                .currentScreenIndex ==
                                            ScreenConstants.webMyObject,
                                        onTap: () {
                                          Provider.of<WebHomeProvider>(context,
                                                      listen: false)
                                                  .currentScreenIndex =
                                              ScreenConstants.webMyObject;
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // My Books text
                          Expanded(
                            child: Text(
                              AppLocalizations.of(context)
                                  .translate('websolution.pageTitle'),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                fontFamily: "TiemposText",
                              ),
                            ),
                          ),
                          // SizedBox(width: 2),

                          // Search bar with filter
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Combined search bar with integrated filter icon
                                Container(
                                  width:
                                      MediaQuery.of(context).size.width / 3.722,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6),
                                    border:
                                        Border.all(color: Colors.grey.shade200),
                                  ),
                                  child: Row(
                                    children: [
                                      // Search text field
                                      Expanded(
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(left: 16.0),
                                          child: TextField(
                                            decoration: InputDecoration(
                                              enabledBorder: InputBorder.none,
                                              focusedBorder: InputBorder.none,
                                              hintText: 'Search',
                                              border: InputBorder.none,
                                              hintStyle: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey[500]),
                                              isDense: true,
                                              contentPadding: EdgeInsets.zero,
                                            ),
                                          ),
                                        ),
                                      ),

                                      // Search icon
                                      _HoverSvgButton(
                                        normalIconPath:
                                            'assets/images/search.svg',
                                        hoverIconPath:
                                            'assets/images/search.svg',
                                        onPressed: () {},
                                        imageWidth: 20,
                                        imageHeight: 20,
                                        showBorderOnHover: false,
                                      ),

                                      // Divider between search and filter
                                      Container(
                                        height: 24,
                                        width: 1,
                                        color: Colors.grey.shade200,
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                      ),

                                      // Filter icon - keeping original properties
                                      _HoverSvgButton(
                                        normalIconPath:
                                            'assets/images/filter-icon.svg',
                                        hoverIconPath:
                                            'assets/images/filter-hover.svg',
                                        onPressed: () {},
                                        imageWidth: 32,
                                        imageHeight: 32,
                                        showBorderOnHover: false,
                                      ),
                                      const SizedBox(width: 8),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Create solution button
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                HoverCreateButton(
                                  text: AppLocalizations.of(context).translate(
                                      'websolution.createButtonText'),
                                  onPressed: () {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants.webBookDetailPage;
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                    ],
                  ),
                )
              : Container(),
          mySolutions(context),
          // Main content
          //pagination
        ],
      ),
    );
  }

  Widget mySolutions(context) {
    return Expanded(
      child: SingleChildScrollView(
        physics: widget.showNavigationBar
            ? const AlwaysScrollableScrollPhysics()
            : const NeverScrollableScrollPhysics(),
        child: Container(
          padding:
              const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),

              // Book grid
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : books.isEmpty
                      ? const Center(child: Text('No books found'))
                      : Consumer<WebHomeProvider>(
                          builder: (context, webHomeProvider, _) {
                            return Center(
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  // Calculate available width considering side panel
                                  double availableWidth = constraints.maxWidth;
                                  if (webHomeProvider.showSidePanel) {
                                    // Subtract approximate side panel width
                                    availableWidth -=
                                        400; // Adjust based on your side panel width
                                  }

                                  // Update items per page based on available width
                                  int newItemsPerPage =
                                      _calculateItemsPerPage(availableWidth);
                                  if (newItemsPerPage != _itemsPerPage) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      setState(() {
                                        _itemsPerPage = newItemsPerPage;
                                        _updatePagination();
                                      });
                                    });
                                  }

                                  // Get books for current page
                                  List<Book> currentPageBooks =
                                      _getCurrentPageBooks();

                                  // Define a desired width for each book card.
                                  const double cardWidth = 136.0;
                                  // Define a minimum spacing between cards.
                                  const double minSpacing = 80;

                                  // Calculate how many cards can fit in the available width.
                                  int booksPerRow =
                                      ((availableWidth + minSpacing) /
                                              (cardWidth + minSpacing))
                                          .floor();

                                  // Ensure at least one book per row to prevent division by zero or awkward layouts.
                                  if (booksPerRow == 0) {
                                    booksPerRow = 1;
                                  }

                                  // Calculate rows needed for current page
                                  int rowCount =
                                      (currentPageBooks.length / booksPerRow)
                                          .ceil();

                                  return Column(
                                    children:
                                        List.generate(rowCount, (rowIndex) {
                                      // Calculate start and end indices for this row
                                      int startIndex = rowIndex * booksPerRow;
                                      int endIndex =
                                          (startIndex + booksPerRow <=
                                                  currentPageBooks.length)
                                              ? startIndex + booksPerRow
                                              : currentPageBooks.length;

                                      // Create a list of books for this row
                                      List<Book> rowBooks = currentPageBooks
                                          .sublist(startIndex, endIndex);

                                      // For the last row with fewer items, calculate positions
                                      bool isLastRowWithFewerItems =
                                          rowBooks.length < booksPerRow &&
                                              rowIndex == rowCount - 1;

                                      return Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 42.0),
                                        child: Row(
                                          mainAxisAlignment:
                                              isLastRowWithFewerItems
                                                  ? MainAxisAlignment.start
                                                  : MainAxisAlignment
                                                      .spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: rowBooks
                                              .asMap()
                                              .entries
                                              .map((entry) {
                                            int idx = entry.key;
                                            Book book = entry.value;

                                            return Padding(
                                              // Apply right padding for items in the last row if they are not the very last item.
                                              // This maintains consistent spacing for full rows and aligns the last row to the start.
                                              padding:
                                                  isLastRowWithFewerItems &&
                                                          idx <
                                                              rowBooks.length -
                                                                  1
                                                      ? const EdgeInsets.only(
                                                          right: minSpacing)
                                                      : EdgeInsets.zero,
                                              child: _HoverBookCard(
                                                onTap: () {
                                                  Provider.of<WebHomeProvider>(
                                                              context,
                                                              listen: false)
                                                          .currentScreenIndex =
                                                      ScreenConstants
                                                          .webBookSolution;
                                                },
                                                child: _buildBookCard(
                                                  title: book.title,
                                                  subtitle: book.subtitle,
                                                  imageUrl: book.imageUrl,
                                                  isDraft: book.isDraft,
                                                  imageWidth: book.imageWidth,
                                                  imageHeight: book.imageHeight,
                                                  index: books.indexOf(book),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                        ),
                                      );
                                    }),
                                  );
                                },
                              ),
                            );
                          },
                        ),
              // Pagination controls
              if (books.isNotEmpty && _totalPages > 1)
                Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Page info
                      Text(
                        'Page $_currentPage of $_totalPages',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontFamily: "TiemposText",
                          color: Colors.grey,
                        ),
                      ),
                      // Navigation buttons
                      Row(
                        children: [
                          // Previous button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_left, size: 20),
                            onPressed:
                                _currentPage > 1 ? _goToPreviousPage : null,
                          ),
                          const SizedBox(width: 8),
                          // Next button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_right, size: 20),
                            onPressed: _currentPage < _totalPages
                                ? _goToNextPage
                                : null,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 136.0,
    double imageHeight = 200.0,
    int index = 0,
  }) {
    // Check if animations are initialized and index is valid
    if (index >= _animationControllers.length) {
      // Fallback to non-animated version if index is out of bounds
      return _buildStaticBookCard(
        title: title,
        subtitle: subtitle,
        imageUrl: imageUrl,
        isDraft: isDraft,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
      );
    }

    return AnimatedBuilder(
      animation: _animationControllers[index],
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_slideAnimations[index].value, 0),
          child: Opacity(
            opacity: _fadeAnimations[index].value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Book cover with animation
                AnimatedBuilder(
                  animation: _animationControllers[index],
                  builder: (context, child) {
                    double scale = 0.9 + (0.1 * _animationControllers[index].value);
                    
                    return Transform.scale(
                      scale: scale,
                      child: Stack(
                        children: [
                          Container(
                            width: imageWidth,
                            height: imageHeight,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage(imageUrl),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          if (isDraft)
                            Positioned(
                              top: 14,
                              right: 14,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  'Draft',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                    fontFamily: "TiemposText",
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: imageWidth,
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                SizedBox(
                  width: imageWidth,
                  child: Text(
                    'Version: 001',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Fallback static version for cases where animations aren't ready
  Widget _buildStaticBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 136.0,
    double imageHeight = 200.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: 14,
                right: 14,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 2),
        SizedBox(
          width: imageWidth,
          child: Text(
            'Version: 001',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 10,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class _HoverIconButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _HoverIconButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverIconButton> createState() => _HoverIconButtonState();
}

class _HoverIconButtonState extends State<_HoverIconButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isHovered ? Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? Colors.grey.shade200
                : isHovered
                    ? Color(0xff0058FF)
                    : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered && !isDisabled ? BorderRadius.zero : null,
          color: isDisabled ? Colors.grey.shade50 : Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isDisabled
              ? Colors.grey.shade400
              : isHovered
                  ? Color(0xff0058FF)
                  : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const _HoverNavItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<_HoverNavItem> createState() => _HoverNavItemState();
}

class _HoverNavItemState extends State<_HoverNavItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isHovered ? Colors.white : Colors.transparent,
            border: Border.all(
              color: isHovered ? Color(0xff0058FF) : Colors.transparent,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                width: 12,
                height: 12,
                colorFilter: ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                  fontWeight:
                      widget.isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _HoverBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _HoverBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_HoverBookCard> createState() => _HoverBookCardState();
}

class _HoverBookCardState extends State<_HoverBookCard> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: widget.child,
      ),
    );
  }
}
