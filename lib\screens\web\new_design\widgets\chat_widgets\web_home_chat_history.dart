import 'package:flutter/material.dart';

// --- Chat Session List Widget ---
class ChatSessionList extends StatefulWidget {
  final bool showNavigationBar;

  const ChatSessionList({super.key, this.showNavigationBar = true});

  @override
  State<ChatSessionList> createState() => _ChatSessionListState();
}

class _ChatSessionListState extends State<ChatSessionList>
    with TickerProviderStateMixin {
  final List<Map<String, String>> sessions = const [
    {"title": "Chat session name To store", "date": "2024-05-01"},
    {"title": "Chat session name To store", "date": "2024-05-02"},
    {"title": "Chat session name To store", "date": "2024-05-03"},
    {"title": "Chat session name To store", "date": "2024-05-04"},
    {"title": "Chat session name To store", "date": "2024-05-05"},
    {"title": "Chat session name To store", "date": "2024-05-06"},
    {"title": "Chat session name To store", "date": "2024-05-07"},
    {"title": "Chat session name To store", "date": "2024-05-08"},
  ];

  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      sessions.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: -30.0, end: 0.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // Start animations with staggered delays (top to bottom)
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.showNavigationBar
          ? const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0)
          : EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.only(left: 6, right: 6),
        child: Column(
          children: [
            const SizedBox(height: 32),
            Expanded(
              child: ListView.separated(
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: sessions.length + 1,
                separatorBuilder: (_, __) => const SizedBox(height: 12),
                itemBuilder: (context, idx) {
                  if (idx < sessions.length) {
                    final session = sessions[idx];
                    return AnimatedBuilder(
                      animation: _animationControllers[idx],
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _slideAnimations[idx].value),
                          child: Opacity(
                            opacity: _fadeAnimations[idx].value,
                            child: ChatSessionListItem(
                              title: session["title"] ?? "",
                              date: session["date"] ?? "",
                              isSelected: idx == 1,
                            ),
                          ),
                        );
                      },
                    );
                  } else {
                    return const PaginationRow();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// --- Chat Session List Item ---
class ChatSessionListItem extends StatelessWidget {
  final String title;
  final String date;
  final bool isSelected;

  const ChatSessionListItem({
    required this.title,
    required this.date,
    this.isSelected = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return HoverBuilder(
      builder: (isHovered) {
        return AnimatedContainer(
          // margin: EdgeInsets.only(right: 20),
          duration: const Duration(milliseconds: 120),
          curve: Curves.easeInOut,
          height: 67,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: isHovered
                ? [
                    BoxShadow(
                      color: const Color(0xFF4A90E2).withValues(alpha: 0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    )
                  ]
                : [],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 17),
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: 14,
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF848484),
                    fontFamily: "TiemposText",
                  ),
                ),
              ),
              Positioned(
                right: 0,
                bottom: 14,
                child: Text(
                  date,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: Color(0xff8D8D8D),
                    fontFamily: "TiemposText",
                    letterSpacing: 1.2,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// --- Hover Builder Utility Widget ---
class HoverBuilder extends StatelessWidget {
  final Widget Function(bool isHovered) builder;

  const HoverBuilder({required this.builder, super.key});

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<bool> isHovered = ValueNotifier(false);

    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: ValueListenableBuilder<bool>(
        valueListenable: isHovered,
        builder: (_, value, child) => builder(value),
      ),
    );
  }
}

// --- Pagination Row ---
class PaginationRow extends StatelessWidget {
  const PaginationRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: const [
          HoverPaginationButton(
            icon: Icon(Icons.chevron_left, size: 20, color: Colors.black),
            onPressed: null,
          ),
          SizedBox(width: 8),
          HoverPaginationButton(
            icon: Icon(Icons.chevron_right, size: 20),
            onPressed: null,
          ),
        ],
      ),
    );
  }
}

// --- Pagination Button ---
class HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const HoverPaginationButton({
    required this.icon,
    this.onPressed,
    super.key,
  });

  @override
  State<HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        // margin: EdgeInsets.only(right: 24),
        decoration: BoxDecoration(
          border: Border.all(
            color: isHovered ? Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isHovered ? Color(0xff0058FF) : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
