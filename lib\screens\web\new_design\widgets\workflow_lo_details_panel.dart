import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/workflow/workflow_lo_response_model.dart';
import '../../../../theme/spacing.dart';

/// A reusable workflow LO details panel widget that displays detailed information about workflow LO data.
///
/// This widget shows workflow LO information including all sections from parsed_data in a structured format
/// with navigation shortcuts and interactive elements.
class WorkflowLoDetailsPanel extends StatefulWidget {
  /// The workflow LO response data to display
  final WorkFlowLoResponseModel workflowLoData;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  const WorkflowLoDetailsPanel({
    super.key,
    required this.workflowLoData,
    this.onClose,
    this.chatController,
    this.onSendMessage,
  });

  @override
  State<WorkflowLoDetailsPanel> createState() => _WorkflowLoDetailsPanelState();
}

class _WorkflowLoDetailsPanelState extends State<WorkflowLoDetailsPanel> {
  // Track expanded state for each section
  Set<String> expandedSectionIds = <String>{};

  @override
  Widget build(BuildContext context) {
    final ScrollController workflowLoDetailScrollController = ScrollController();
    final Map<String, GlobalKey> sectionKeys = {
      'localObjectives': GlobalKey(),
      'loInputStack': GlobalKey(),
      'loInputItems': GlobalKey(),
      'loOutputStack': GlobalKey(),
      'loOutputItems': GlobalKey(),
      'loEntityValidations': GlobalKey(),
      'loUiStack': GlobalKey(),
      'loUiEntityAttributeStack': GlobalKey(),
      'loDataMappingStack': GlobalKey(),
      'loDataMappings': GlobalKey(),
      'loNestedFunctions': GlobalKey(),
      'executionPathways': GlobalKey(),
      'executionPathwayConditions': GlobalKey(),
      'loNestedFunctionInputStacks': GlobalKey(),
      'loNestedFunctionOutputItems': GlobalKey(),
      'loNestedFunctionOutputStacks': GlobalKey(),
    };

    void scrollToSection(String sectionId) {
      if (sectionKeys.containsKey(sectionId)) {
        final RenderObject? renderObject =
            sectionKeys[sectionId]?.currentContext?.findRenderObject();
        if (renderObject != null) {
          workflowLoDetailScrollController.position.ensureVisible(
            renderObject,
            alignment: 0.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xff9B9B9B).withValues(alpha: 0.14),
            blurRadius: 20,
            offset: Offset(-3, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and close button
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(width: 30),
                      CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),
                        radius: 10,
                        child: Icon(
                          Icons.account_tree_outlined,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xs),
                      Expanded(
                        child: Text(
                          'Workflow LO Details',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText",
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {},
                ),
                const SizedBox(
                  width: AppSpacing.xxs,
                ),
                IconButton(
                  icon: SvgPicture.asset(
                    'assets/images/chat/toggle_open_close.svg',
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      Colors.grey.shade700,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: widget.onClose,
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          // Content area with navigation labels and sections
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left column for navigation labels
                Container(
                  width: 40,
                  padding: EdgeInsets.only(top: 3),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildNavigationLabel('LO', 'localObjectives', scrollToSection),
                        _buildNavigationLabel('IS', 'loInputStack', scrollToSection),
                        _buildNavigationLabel('II', 'loInputItems', scrollToSection),
                        _buildNavigationLabel('OS', 'loOutputStack', scrollToSection),
                        _buildNavigationLabel('OI', 'loOutputItems', scrollToSection),
                        _buildNavigationLabel('EV', 'loEntityValidations', scrollToSection),
                        _buildNavigationLabel('US', 'loUiStack', scrollToSection),
                        _buildNavigationLabel('UA', 'loUiEntityAttributeStack', scrollToSection),
                        _buildNavigationLabel('DM', 'loDataMappingStack', scrollToSection),
                        _buildNavigationLabel('DG', 'loDataMappings', scrollToSection),
                        _buildNavigationLabel('NF', 'loNestedFunctions', scrollToSection),
                        _buildNavigationLabel('EP', 'executionPathways', scrollToSection),
                        _buildNavigationLabel('EC', 'executionPathwayConditions', scrollToSection),
                        _buildNavigationLabel('NI', 'loNestedFunctionInputStacks', scrollToSection),
                        _buildNavigationLabel('NO', 'loNestedFunctionOutputItems', scrollToSection),
                        _buildNavigationLabel('NS', 'loNestedFunctionOutputStacks', scrollToSection),
                      ],
                    ),
                  ),
                ),

                // Right column for content
                Expanded(
                  child: SingleChildScrollView(
                    controller: workflowLoDetailScrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildLocalObjectivesSection(context, sectionKeys),
                        _buildLoInputStackSection(context, sectionKeys),
                        _buildLoInputItemsSection(context, sectionKeys),
                        _buildLoOutputStackSection(context, sectionKeys),
                        _buildLoOutputItemsSection(context, sectionKeys),
                        _buildLoEntityValidationsSection(context, sectionKeys),
                        _buildLoUiStackSection(context, sectionKeys),
                        _buildLoUiEntityAttributeStackSection(context, sectionKeys),
                        _buildLoDataMappingStackSection(context, sectionKeys),
                        _buildLoDataMappingsSection(context, sectionKeys),
                        _buildLoNestedFunctionsSection(context, sectionKeys),
                        _buildExecutionPathwaysSection(context, sectionKeys),
                        _buildExecutionPathwayConditionsSection(context, sectionKeys),
                        _buildLoNestedFunctionInputStacksSection(context, sectionKeys),
                        _buildLoNestedFunctionOutputItemsSection(context, sectionKeys),
                        _buildLoNestedFunctionOutputStacksSection(context, sectionKeys),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a navigation label widget
  Widget _buildNavigationLabel(String label, String sectionId, Function(String) onTap) {
    return Padding(
      padding: EdgeInsets.only(left: 8, top: 16),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () => onTap(sectionId),
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 10,
              color: Colors.blue.shade700,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a generic section widget
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return Container(
      key: sectionKeys[sectionId],
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$title:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              fontFamily: "TiemposText",
            ),
          ),
          SizedBox(height: 8),
          hasData ? contentBuilder() : _buildNoDataWidget(title),
        ],
      ),
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Text(
      'No $sectionName data available.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the local objectives section
  Widget _buildLocalObjectivesSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'localObjectives',
      'Local Objectives',
      widget.workflowLoData.parsedData?.localObjectives != null,
      () => _buildLocalObjectivesContent(context),
    );
  }

  /// Builds the local objectives content
  Widget _buildLocalObjectivesContent(BuildContext context) {
    final localObjectives = widget.workflowLoData.parsedData!.localObjectives!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('Name', localObjectives.name ?? 'N/A'),
        _buildDetailItem('Version', localObjectives.version ?? 'N/A'),
        _buildDetailItem('Status', localObjectives.status ?? 'N/A'),
        _buildDetailItem('Function Type', localObjectives.functionType ?? 'N/A'),
        _buildDetailItem('Agent Type', localObjectives.agentType?.toString() ?? 'N/A'),
        _buildDetailItem('Execution Rights', localObjectives.executionRights ?? 'N/A'),
        _buildDetailItem('GO ID', localObjectives.goId ?? 'N/A'),
        _buildDetailItem('LO ID', localObjectives.loId?.toString() ?? 'N/A'),
      ],
    );
  }

  /// Builds a detail item
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• $label: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontFamily: "TiemposText",
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the LO input stack section
  Widget _buildLoInputStackSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loInputStack',
      'LO Input Stack',
      widget.workflowLoData.parsedData?.loInputStack != null,
      () => _buildLoInputStackContent(context),
    );
  }

  /// Builds the LO input stack content
  Widget _buildLoInputStackContent(BuildContext context) {
    final inputStack = widget.workflowLoData.parsedData!.loInputStack!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('ID', inputStack.id ?? 'N/A'),
        _buildDetailItem('LO ID', inputStack.loId?.toString() ?? 'N/A'),
        _buildDetailItem('Description', inputStack.description ?? 'N/A'),
      ],
    );
  }

  /// Builds the LO input items section
  Widget _buildLoInputItemsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loInputItems',
      'LO Input Items',
      widget.workflowLoData.parsedData?.loInputItems != null &&
      widget.workflowLoData.parsedData!.loInputItems!.isNotEmpty,
      () => _buildLoInputItemsContent(context),
    );
  }

  /// Builds the LO input items content with tabs
  Widget _buildLoInputItemsContent(BuildContext context) {
    final inputItems = widget.workflowLoData.parsedData!.loInputItems!;

    return DefaultTabController(
      length: inputItems.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TabBar(
            isScrollable: true,
            labelColor: Colors.blue.shade700,
            unselectedLabelColor: Colors.grey.shade600,
            indicatorColor: Colors.blue.shade700,
            tabs: inputItems.asMap().entries.map((entry) {
              int index = entry.key;
              return Tab(text: 'Item ${index + 1}');
            }).toList(),
          ),
          SizedBox(
            height: 200,
            child: TabBarView(
              children: inputItems.map((item) {
                return SingleChildScrollView(
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem('ID', item.id ?? 'N/A'),
                      _buildDetailItem('Input Stack ID', item.inputStackId?.toString() ?? 'N/A'),
                      _buildDetailItem('Slot ID', item.slotId ?? 'N/A'),
                      _buildDetailItem('Required', item.required?.toString() ?? 'N/A'),
                      _buildDetailItem('Data Type', item.dataType?.toString() ?? 'N/A'),
                      _buildDetailItem('Dependent Attribute', item.dependentAttribute?.toString() ?? 'N/A'),
                      _buildDetailItem('Agent Type', item.agentType?.toString() ?? 'N/A'),
                      _buildDetailItem('Source Type', item.sourceType?.toString() ?? 'N/A'),
                      _buildDetailItem('Source Description', item.sourceDescription ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  // Placeholder methods for remaining sections
  Widget _buildLoOutputStackSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loOutputStack', 'LO Output Stack',
        widget.workflowLoData.parsedData?.loOutputStack != null, () => _buildNoDataWidget('LO Output Stack'));
  }

  Widget _buildLoOutputItemsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loOutputItems', 'LO Output Items',
        widget.workflowLoData.parsedData?.loOutputItems != null, () => _buildNoDataWidget('LO Output Items'));
  }

  Widget _buildLoEntityValidationsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loEntityValidations', 'LO Entity Validations',
        widget.workflowLoData.parsedData?.loEntityValidations != null, () => _buildNoDataWidget('LO Entity Validations'));
  }

  Widget _buildLoUiStackSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loUiStack', 'LO UI Stack',
        widget.workflowLoData.parsedData?.loUiStack != null, () => _buildNoDataWidget('LO UI Stack'));
  }

  Widget _buildLoUiEntityAttributeStackSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loUiEntityAttributeStack', 'LO UI Entity Attribute Stack',
        widget.workflowLoData.parsedData?.loUiEntityAttributeStack != null, () => _buildNoDataWidget('LO UI Entity Attribute Stack'));
  }

  Widget _buildLoDataMappingStackSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loDataMappingStack', 'LO Data Mapping Stack',
        widget.workflowLoData.parsedData?.loDataMappingStack != null, () => _buildNoDataWidget('LO Data Mapping Stack'));
  }

  Widget _buildLoDataMappingsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loDataMappings', 'LO Data Mappings',
        widget.workflowLoData.parsedData?.loDataMappings != null, () => _buildNoDataWidget('LO Data Mappings'));
  }

  Widget _buildLoNestedFunctionsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loNestedFunctions', 'LO Nested Functions',
        widget.workflowLoData.parsedData?.loNestedFunctions != null, () => _buildNoDataWidget('LO Nested Functions'));
  }

  Widget _buildExecutionPathwaysSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'executionPathways', 'Execution Pathways',
        widget.workflowLoData.parsedData?.executionPathways != null, () => _buildNoDataWidget('Execution Pathways'));
  }

  Widget _buildExecutionPathwayConditionsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'executionPathwayConditions', 'Execution Pathway Conditions',
        widget.workflowLoData.parsedData?.executionPathwayConditions != null, () => _buildNoDataWidget('Execution Pathway Conditions'));
  }

  Widget _buildLoNestedFunctionInputStacksSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loNestedFunctionInputStacks', 'LO Nested Function Input Stacks',
        widget.workflowLoData.parsedData?.loNestedFunctionInputStacks != null, () => _buildNoDataWidget('LO Nested Function Input Stacks'));
  }

  Widget _buildLoNestedFunctionOutputItemsSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loNestedFunctionOutputItems', 'LO Nested Function Output Items',
        widget.workflowLoData.parsedData?.loNestedFunctionOutputItems != null, () => _buildNoDataWidget('LO Nested Function Output Items'));
  }

  Widget _buildLoNestedFunctionOutputStacksSection(BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(context, sectionKeys, 'loNestedFunctionOutputStacks', 'LO Nested Function Output Stacks',
        widget.workflowLoData.parsedData?.loNestedFunctionOutputStacks != null, () => _buildNoDataWidget('LO Nested Function Output Stacks'));
  }
}
